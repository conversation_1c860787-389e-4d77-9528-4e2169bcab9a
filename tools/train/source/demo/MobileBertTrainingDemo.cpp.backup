//
//  MobileBertTrainingDemo.cpp
//  MNN
//
//  MobileBert Mobile Training Demo using Generated Express Code
//  支持文本分类任务的手机端GPU训练
//

#include <MNN/expr/Executor.hpp>
#include <MNN/expr/Optimizer.hpp>
#include <MNN/expr/Module.hpp>
#include <cmath>
#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <memory>
#include <chrono>
#include <algorithm>
#include <thread>
#include <fstream>
#include "DemoUnit.hpp"
#include "DataLoader.hpp"
#include "ADAM.hpp"
#include "SGD.hpp"
#include "LearningRateScheduler.hpp"
#include "Loss.hpp"
#include "RandomGenerator.hpp"
#include "NN.hpp"
#include "Transformer.hpp"
#include "module/PipelineModule.hpp"
#include "MobileBertModel.hpp"

using namespace MNN;
using namespace MNN::Express;
using namespace MNN::Train;
using namespace std;
using namespace std::chrono;

// 常量定义 - 避免重复定义
// const int MAX_SEQ_LENGTH = 128;  // 已在MobileBertModel.hpp中定义
const int VOCAB_SIZE = 30522;    // MobileBert词汇表大小
const int NUM_CLASSES = 2;       // 二分类任务

/**
 * MobileBert文本数据集类
 * 处理文本分类数据的加载和预处理
 */
class MobileBertTextDataset {
public:
    struct TextSample {
        std::vector<int> input_ids;
        std::vector<int> attention_mask;
        std::vector<int> token_type_ids;
        int label;
    };
    
    static std::vector<TextSample> loadDataset(const std::string& dataPath, const std::string& txtPath) {
        std::vector<TextSample> samples;
        std::ifstream file(txtPath);
        std::string line;
        
        while (std::getline(file, line)) {
            std::istringstream iss(line);
            std::string filename;
            int label;
            
            if (iss >> filename >> label) {
                TextSample sample;
                
                // 简化的tokenization - 实际应用中需要使用真正的tokenizer
                // 这里生成模拟的token序列
                sample.input_ids.resize(MAX_SEQ_LENGTH, 0);
                sample.attention_mask.resize(MAX_SEQ_LENGTH, 1);
                sample.token_type_ids.resize(MAX_SEQ_LENGTH, 0);
                
                // [CLS] token
                sample.input_ids[0] = 101;
                
                // 模拟文本tokens (基于文件名生成)
                for (int i = 1; i < std::min(MAX_SEQ_LENGTH - 1, 50); ++i) {
                    sample.input_ids[i] = 1000 + (filename.length() * i) % 20000;
                }
                
                // [SEP] token
                int seq_len = std::min(MAX_SEQ_LENGTH - 1, 50);
                sample.input_ids[seq_len] = 102;
                
                // 设置attention mask
                for (int i = seq_len + 1; i < MAX_SEQ_LENGTH; ++i) {
                    sample.attention_mask[i] = 0;
                }
                
                sample.label = label;
                samples.push_back(sample);
            }
        }
        
        return samples;
    }
    
    struct BatchData {
        VARP input_ids;
        VARP attention_mask;
        VARP token_type_ids;
        VARP labels;
    };

    static BatchData createBatch(const std::vector<TextSample>& samples,
                                int start_idx, int batch_size) {
        int actual_batch_size = std::min(batch_size, (int)samples.size() - start_idx);

        // 创建输入张量
        auto input_ids = _Input({actual_batch_size, MAX_SEQ_LENGTH}, NCHW, halide_type_of<int>());
        auto attention_mask = _Input({actual_batch_size, MAX_SEQ_LENGTH}, NCHW, halide_type_of<int>());
        auto token_type_ids = _Input({actual_batch_size, MAX_SEQ_LENGTH}, NCHW, halide_type_of<int>());
        auto labels = _Input({actual_batch_size}, NCHW, halide_type_of<int>());

        // 填充数据
        auto input_ids_ptr = input_ids->writeMap<int>();
        auto attention_mask_ptr = attention_mask->writeMap<int>();
        auto token_type_ids_ptr = token_type_ids->writeMap<int>();
        auto labels_ptr = labels->writeMap<int>();

        for (int i = 0; i < actual_batch_size; ++i) {
            const auto& sample = samples[start_idx + i];

            for (int j = 0; j < MAX_SEQ_LENGTH; ++j) {
                input_ids_ptr[i * MAX_SEQ_LENGTH + j] = sample.input_ids[j];
                attention_mask_ptr[i * MAX_SEQ_LENGTH + j] = sample.attention_mask[j];
                token_type_ids_ptr[i * MAX_SEQ_LENGTH + j] = sample.token_type_ids[j];
            }

            labels_ptr[i] = sample.label;
        }

        input_ids->unMap();
        attention_mask->unMap();
        token_type_ids->unMap();
        labels->unMap();

        return {input_ids, attention_mask, token_type_ids, labels};
    }
};

// MobileBert GPU训练工具类 - 优化为FP32+CrossEntropy+计时
class MobileBertGPUTrainer {
public:
    static void train(MNNForwardType backend, int threadNumber, std::shared_ptr<Module> model,
                     const int numClasses, const int addToLabel,
                     std::string trainDataFolder, std::string trainDataTxt,
                     std::string testDataFolder, std::string testDataTxt, 
                     int maxSeqLength = MAX_SEQ_LENGTH, int batchSize = 1) {
        
        auto exe = Executor::getGlobalExecutor();
        BackendConfig config;
        config.precision = BackendConfig::Precision_High; // FP32
        config.power = BackendConfig::Power_High;         // 高性能模式
        exe->setGlobalExecutorConfig(backend, config, threadNumber);
        
        std::cout << "\n=== MobileBert GPU Training Configuration ===" << std::endl;
        std::cout << "Backend: " << (backend == MNN_FORWARD_OPENCL ? "GPU (OpenCL)" : "CPU") << std::endl;
        std::cout << "Precision: FP32" << std::endl;
        std::cout << "Max Sequence Length: " << maxSeqLength << std::endl;
        std::cout << "Batch Size: " << batchSize << std::endl;
        std::cout << "Num Classes: " << numClasses << std::endl;
        std::cout << "=============================================" << std::endl;
        
        // 加载数据集
        std::cout << "Loading training dataset..." << std::endl;
        auto trainSamples = MobileBertTextDataset::loadDataset(trainDataFolder, trainDataTxt);
        std::cout << "Loaded " << trainSamples.size() << " training samples" << std::endl;
        
        if (trainSamples.empty()) {
            std::cout << "Error: No training samples loaded!" << std::endl;
            return;
        }
        
        // 设置训练参数
        const float learningRate = 0.001f;
        const int epochs = 2;
        const int iterations_per_epoch = std::min(20, (int)trainSamples.size() / batchSize);
        
        std::cout << "Training parameters:" << std::endl;
        std::cout << "  Learning Rate: " << learningRate << std::endl;
        std::cout << "  Epochs: " << epochs << std::endl;
        std::cout << "  Iterations per epoch: " << iterations_per_epoch << std::endl;
        
        // 创建优化器
        auto optimizer = std::shared_ptr<SGD>(new SGD(model));
        optimizer->setLearningRate(learningRate);
        
        // 训练循环
        auto start_time = high_resolution_clock::now();
        
        for (int epoch = 0; epoch < epochs; ++epoch) {
            std::cout << "\n--- Epoch " << (epoch + 1) << "/" << epochs << " ---" << std::endl;
            
            float total_loss = 0.0f;
            int total_correct = 0;
            int total_samples = 0;
            
            for (int iter = 0; iter < iterations_per_epoch; ++iter) {
                int start_idx = (iter * batchSize) % trainSamples.size();

                // 创建batch
                auto batch_data = MobileBertTextDataset::createBatch(trainSamples, start_idx, batchSize);

                // 前向传播
                auto outputs = model->onForward({batch_data.input_ids, batch_data.attention_mask, batch_data.token_type_ids});
                if (outputs.empty()) {
                    std::cout << "Error: Model forward failed!" << std::endl;
                    continue;
                }

                auto logits = outputs[0];
                
                // 计算损失 (CrossEntropy)
                auto loss = _CrossEntropy(logits, batch_data.labels);

                // 反向传播
                optimizer->step(loss);

                // 统计
                float loss_val = loss->readMap<float>()[0];
                total_loss += loss_val;

                // 计算准确率
                auto predictions = _ArgMax(logits, 1);
                auto correct = _Equal(predictions, batch_data.labels);
                auto correct_count = _ReduceSum(correct, {});
                total_correct += correct_count->readMap<int>()[0];
                total_samples += batchSize;
                
                if ((iter + 1) % 5 == 0) {
                    float avg_loss = total_loss / (iter + 1);
                    float accuracy = (float)total_correct / total_samples * 100.0f;
                    std::cout << "  Iter " << (iter + 1) << "/" << iterations_per_epoch 
                              << " - Loss: " << std::fixed << std::setprecision(4) << avg_loss
                              << " - Acc: " << std::setprecision(2) << accuracy << "%" << std::endl;
                }
            }
            
            float epoch_loss = total_loss / iterations_per_epoch;
            float epoch_accuracy = (float)total_correct / total_samples * 100.0f;
            
            std::cout << "Epoch " << (epoch + 1) << " completed - Loss: " 
                      << std::fixed << std::setprecision(4) << epoch_loss
                      << " - Accuracy: " << std::setprecision(2) << epoch_accuracy << "%" << std::endl;
        }
        
        auto end_time = high_resolution_clock::now();
        auto duration = duration_cast<seconds>(end_time - start_time);
        
        std::cout << "\n=== Training Completed ===" << std::endl;
        std::cout << "Total training time: " << duration.count() << " seconds" << std::endl;
        std::cout << "Training successful! ✅" << std::endl;
    }
};

// MobileBert训练Demo类
class MobileBertTrainingDemo : public DemoUnit {
public:
    virtual int run(int argc, const char* argv[]) override {
        if (argc < 3) {
            std::cout << "usage: ./runTrainDemo.out MobileBertTrainingDemo path/to/train/data/ path/to/train/data.txt [backend_type] [batch_size]" << std::endl;
            std::cout << "backend_type: 0=CPU, 3=GPU(OpenCL)" << std::endl;
            std::cout << "batch_size: 1,2,4,8,16 (default: 1)" << std::endl;
            return 0;
        }

        // 默认使用GPU训练，可通过参数指定
        MNNForwardType type = MNN_FORWARD_OPENCL;
        if (argc >= 4) {
            int backendType = atoi(argv[3]);
            if (backendType == 0) {
                type = MNN_FORWARD_CPU;
            }
        }

        // 获取batch size
        int batchSize = 1;
        if (argc >= 5) {
            batchSize = atoi(argv[4]);
            if (batchSize <= 0 || batchSize > 16) {
                std::cout << "Invalid batch size. Using default: 1" << std::endl;
                batchSize = 1;
            }
        }

        std::cout << "=== MobileBert Mobile Training Demo ===" << std::endl;
        std::cout << "Backend: " << (type == MNN_FORWARD_OPENCL ? "GPU (OpenCL)" : "CPU") << std::endl;
        std::cout << "Batch Size: " << batchSize << std::endl;
        std::cout << "Max Sequence Length: " << MAX_SEQ_LENGTH << std::endl;
        std::cout << "Vocab Size: " << VOCAB_SIZE << std::endl;
        std::cout << "Num Classes: " << NUM_CLASSES << std::endl;
        std::cout << "=========================================" << std::endl;

        // 设置随机种子
        RandomGenerator::generator(42);

        std::string trainDataFolder = argv[1];
        std::string trainDataTxt = argv[2];
        std::string testDataFolder = argv[1]; // 使用相同的数据进行测试
        std::string testDataTxt = argv[2];

        // 创建MobileBert训练模型
        std::shared_ptr<Module> model(new MobileBertModel(NUM_CLASSES, VOCAB_SIZE, MAX_SEQ_LENGTH));

        // 打印模型信息
        static_cast<MobileBertModel*>(model.get())->printModelInfo();

        // 开始GPU训练（FP32 + CrossEntropy + 计时 + 可变batch size）
        MobileBertGPUTrainer::train(type, 1, model, NUM_CLASSES, 0,
                                   trainDataFolder, trainDataTxt,
                                   testDataFolder, testDataTxt, MAX_SEQ_LENGTH, batchSize);

        return 0;
    }
};

DemoUnitSetRegister(MobileBertTrainingDemo, "MobileBertTrainingDemo");
