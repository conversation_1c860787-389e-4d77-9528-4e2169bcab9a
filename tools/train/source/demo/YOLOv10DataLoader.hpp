//
//  YOLOv10DataLoader.hpp
//  MNN
//
//  Created for YOLOv10 Object Detection Training
//  基于EfficientNetDataLoader.hpp改编，适用于目标检测任务
//

#ifndef YOLOv10DataLoader_hpp
#define YOLOv10DataLoader_hpp

#include <MNN/expr/Module.hpp>
#include <string>
#include "ImageDataset.hpp"
#include "DataLoader.hpp"

using namespace MNN;
using namespace MNN::Express;
using namespace MNN::Train;

class YOLOv10DataLoader {
public:
    static std::shared_ptr<ImageDataset::ImageConfig> createConfig() {
        // YOLOv10 使用 RGB 格式，320x320 输入尺寸
        auto converImagesToFormat = CV::RGB;
        int resizeHeight = 320;
        int resizeWidth = 320;
        
        // YOLOv10 通常使用简单的归一化，不使用ImageNet预处理
        // 将像素值从[0,255]归一化到[0,1]
        std::vector<float> means = {0.0f, 0.0f, 0.0f};
        std::vector<float> scales = {1.0f / 255.0f, 1.0f / 255.0f, 1.0f / 255.0f};
        
        // 不进行裁剪，直接resize到目标尺寸
        std::vector<float> cropFraction = {1.0f, 1.0f};
        bool centerOrRandomCrop = false; // false表示中心裁剪
        
        return std::shared_ptr<ImageDataset::ImageConfig>(
            ImageDataset::ImageConfig::create(converImagesToFormat, resizeHeight, resizeWidth, 
                                            scales, means, cropFraction, centerOrRandomCrop)
        );
    }
    
    static DatasetPtr createDataset(const std::string& trainImagesFolder, 
                                   const std::string& trainImagesTxt,
                                   bool readAllImagesToMemory = false) {
        auto config = createConfig();
        return ImageDataset::create(trainImagesFolder, trainImagesTxt, config.get(), readAllImagesToMemory);
    }
    
    static std::shared_ptr<DataLoader> createDataLoader(const std::string& trainImagesFolder,
                                                       const std::string& trainImagesTxt,
                                                       int batchSize = 1,
                                                       bool shuffle = true,
                                                       bool readAllImagesToMemory = false) {
        auto dataset = createDataset(trainImagesFolder, trainImagesTxt, readAllImagesToMemory);
        auto dataLoader = std::shared_ptr<DataLoader>(dataset->createLoader(batchSize, shuffle));
        return dataLoader;
    }
    
    // YOLOv10 特定的输入尺寸信息
    static constexpr int INPUT_HEIGHT = 320;
    static constexpr int INPUT_WIDTH = 320;
    static constexpr int INPUT_CHANNELS = 3;
    
    /**
     * 创建用于目标检测的配置
     * 支持数据增强选项
     */
    static std::shared_ptr<ImageDataset::ImageConfig> createDetectionConfig(bool useAugmentation = false) {
        auto converImagesToFormat = CV::RGB;
        int resizeHeight = 320;
        int resizeWidth = 320;
        
        // YOLOv10 数据预处理
        std::vector<float> means = {0.0f, 0.0f, 0.0f};
        std::vector<float> scales = {1.0f / 255.0f, 1.0f / 255.0f, 1.0f / 255.0f};
        
        // 根据是否使用数据增强来设置裁剪参数
        std::vector<float> cropFraction;
        bool centerOrRandomCrop;
        
        if (useAugmentation) {
            // 使用随机裁剪进行数据增强
            cropFraction = {0.8f, 1.0f}; // 随机裁剪80%-100%的区域
            centerOrRandomCrop = true; // 随机裁剪
        } else {
            // 不使用数据增强，直接resize
            cropFraction = {1.0f, 1.0f};
            centerOrRandomCrop = false; // 中心裁剪
        }
        
        return std::shared_ptr<ImageDataset::ImageConfig>(
            ImageDataset::ImageConfig::create(converImagesToFormat, resizeHeight, resizeWidth, 
                                            scales, means, cropFraction, centerOrRandomCrop)
        );
    }
    
    /**
     * 创建用于目标检测的数据集
     * 支持数据增强
     */
    static DatasetPtr createDetectionDataset(const std::string& trainImagesFolder, 
                                            const std::string& trainImagesTxt,
                                            bool useAugmentation = false,
                                            bool readAllImagesToMemory = false) {
        auto config = createDetectionConfig(useAugmentation);
        return ImageDataset::create(trainImagesFolder, trainImagesTxt, config.get(), readAllImagesToMemory);
    }
    
    /**
     * 创建用于目标检测的数据加载器
     * 支持数据增强和灵活的批处理配置
     */
    static std::shared_ptr<DataLoader> createDetectionDataLoader(const std::string& trainImagesFolder,
                                                               const std::string& trainImagesTxt,
                                                               int batchSize = 1,
                                                               bool shuffle = true,
                                                               bool useAugmentation = false,
                                                               bool readAllImagesToMemory = false) {
        auto dataset = createDetectionDataset(trainImagesFolder, trainImagesTxt, useAugmentation, readAllImagesToMemory);
        auto dataLoader = std::shared_ptr<DataLoader>(dataset->createLoader(batchSize, shuffle));
        return dataLoader;
    }
    
    /**
     * 获取推荐的训练配置
     */
    static void printRecommendedConfig() {
        std::cout << "=== YOLOv10 Data Loading Configuration ===" << std::endl;
        std::cout << "Input Size: " << INPUT_WIDTH << "x" << INPUT_HEIGHT << "x" << INPUT_CHANNELS << std::endl;
        std::cout << "Color Format: RGB" << std::endl;
        std::cout << "Normalization: [0,255] -> [0,1]" << std::endl;
        std::cout << "Data Augmentation: Optional (random crop)" << std::endl;
        std::cout << "Recommended Batch Sizes: 1, 4, 8, 16, 32, 64" << std::endl;
        std::cout << "Memory Loading: Recommended false for large datasets" << std::endl;
        std::cout << "Shuffle: Recommended true for training" << std::endl;
        std::cout << "=========================================" << std::endl;
    }
    
    /**
     * 验证数据加载器配置
     */
    static bool validateConfig(int batchSize, bool useGPU) {
        // 检查batch size是否合理
        std::vector<int> supportedBatchSizes = {1, 4, 8, 16, 32, 64};
        bool validBatchSize = false;
        for (int size : supportedBatchSizes) {
            if (batchSize == size) {
                validBatchSize = true;
                break;
            }
        }
        
        if (!validBatchSize) {
            std::cout << "Warning: Batch size " << batchSize << " may not be optimal." << std::endl;
            std::cout << "Recommended batch sizes: ";
            for (size_t i = 0; i < supportedBatchSizes.size(); i++) {
                std::cout << supportedBatchSizes[i];
                if (i < supportedBatchSizes.size() - 1) std::cout << ", ";
            }
            std::cout << std::endl;
        }
        
        // 检查GPU配置
        if (useGPU && batchSize > 32) {
            std::cout << "Warning: Large batch size (" << batchSize 
                     << ") with GPU may cause memory issues on mobile devices." << std::endl;
        }
        
        return validBatchSize;
    }
};

#endif /* YOLOv10DataLoader_hpp */
