//
//  YOLOv10TrainingDemo.cpp
//  MNN
//
//  YOLOv10 Mobile Training Demo using Generated Express Code
//  基于EfficientNet训练代码改编，适用于目标检测任务
//

#include <MNN/expr/Executor.hpp>
#include <MNN/expr/Optimizer.hpp>
#include <MNN/expr/Module.hpp>
#include <cmath>
#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <memory>
#include <chrono>
#include <algorithm>
#include <thread>
#include "DemoUnit.hpp"
#include "ImageDataset.hpp"
#include "DataLoader.hpp"
#include "ADAM.hpp"
#include "SGD.hpp"
#include "LearningRateScheduler.hpp"
#include "Loss.hpp"
#include "RandomGenerator.hpp"
#include "NN.hpp"
#include "Transformer.hpp"
#include "module/PipelineModule.hpp"
#include "YOLOv10Model.hpp"

using namespace MNN;
using namespace MNN::Express;
using namespace MNN::Train;
using namespace std;
using namespace std::chrono;

// 常量定义
const int INPUT_SIZE = 320;  // YOLOv10输入尺寸
const int NUM_CLASSES = 5;   // 类别数 (可调整)

// YOLOv10 GPU训练工具类 - 优化为FP32+目标检测损失+计时
class YOLOv10GPUTrainer {
public:
    static void train(MNNForwardType backend, int threadNumber, std::shared_ptr<Module> model,
                     const int numClasses, const int addToLabel,
                     std::string trainImagesFolder, std::string trainImagesTxt,
                     std::string testImagesFolder, std::string testImagesTxt, int size = INPUT_SIZE, int batchSize = 1) {

        // 配置执行器 - 平衡性能和热节流
        auto exe = Executor::getGlobalExecutor();
        BackendConfig config;
        config.precision = BackendConfig::Precision_High; // FP32精度
        config.power = BackendConfig::Power_Normal; // 普通功耗模式（避免过热）
        exe->setGlobalExecutorConfig(backend, config, threadNumber);

        cout << "=== YOLOv10 GPU Training Configuration ===" << endl;
        cout << "Backend: " << (backend == MNN_FORWARD_OPENCL ? "GPU (OpenCL)" : "CPU") << endl;
        cout << "Precision: FP32" << endl;
        cout << "Task: Object Detection" << endl;
        cout << "Loss Function: Simplified Detection Loss (MSE-based)" << endl;
        cout << "Batch Size: " << batchSize << endl;
        cout << "Learning Rate: Constant (0.001)" << endl;
        cout << "Focus: Performance Testing for YOLOv10" << endl;
        cout << "=========================================" << endl;
        
        // 使用ADAM优化器
        std::shared_ptr<SGD> solver(new ADAM(model));
        solver->setMomentum(0.9f);
        solver->setWeightDecay(0.00004f);

        // 数据预处理配置 - 适用于目标检测
        auto converImagesToFormat = CV::RGB;
        int resizeHeight = size;
        int resizeWidth = size;
        std::vector<float> means = {0.0f, 0.0f, 0.0f}; // YOLOv10通常不使用ImageNet预处理
        std::vector<float> scales = {1.0f/255.0f, 1.0f/255.0f, 1.0f/255.0f}; // 归一化到[0,1]
        std::vector<float> cropFraction = {1.0f, 1.0f}; // 不裁剪，直接resize
        bool centerOrRandomCrop = false;
        
        std::shared_ptr<ImageDataset::ImageConfig> datasetConfig(
            ImageDataset::ImageConfig::create(converImagesToFormat, resizeHeight, resizeWidth, 
                                             scales, means, cropFraction, centerOrRandomCrop));
        
        bool readAllImagesToMemory = false;
        auto trainDataset = ImageDataset::create(trainImagesFolder, trainImagesTxt, datasetConfig.get(), readAllImagesToMemory);
        auto testDataset = ImageDataset::create(testImagesFolder, testImagesTxt, datasetConfig.get(), readAllImagesToMemory);

        // 训练配置 - 支持可变batch size
        const int trainBatchSize = batchSize;
        const int trainNumWorkers = 0; // 移动设备不使用多线程
        const int testBatchSize = batchSize;
        const int testNumWorkers = 0;

        auto trainDataLoader = trainDataset.createLoader(trainBatchSize, true, true, trainNumWorkers);
        auto testDataLoader = testDataset.createLoader(testBatchSize, true, false, testNumWorkers);

        // 固定iteration数量，确保所有batch size都有相同的训练iteration
        const int FIXED_ITERATIONS_PER_EPOCH = 20;
        const int maxTrainIterations = trainDataLoader->iterNumber();
        const int maxTestIterations = testDataLoader->iterNumber();

        // 使用固定iteration数量或数据集最大iteration数量中的较小值
        const int trainIterations = (FIXED_ITERATIONS_PER_EPOCH < maxTrainIterations) ? FIXED_ITERATIONS_PER_EPOCH : maxTrainIterations;
        const int testIterations = (FIXED_ITERATIONS_PER_EPOCH < maxTestIterations) ? FIXED_ITERATIONS_PER_EPOCH : maxTestIterations;

        cout << "=== YOLOv10 Object Detection Training Started ===" << endl;
        cout << "Backend: " << (backend == MNN_FORWARD_OPENCL ? "GPU (OpenCL)" : "CPU") << endl;
        cout << "Model: YOLOv10 for object detection" << endl;
        cout << "Fixed iterations per epoch: " << trainIterations << endl;
        cout << "Max available iterations: " << maxTrainIterations << endl;
        cout << "Test iterations per epoch: " << testIterations << endl;

        // 训练循环 - 只训练2个epoch进行测试
        for (int epoch = 0; epoch < 2; ++epoch) {
            model->clearCache();
            std::cout << "\n--- Epoch " << (epoch + 1) << "/2 ---" << std::endl;
            
            // 训练阶段
            trainDataLoader->reset();
            model->setIsTraining(true);
            float epochLoss = 0.0f;
            
            for (int i = 0; i < trainIterations; i++) {
                // 开始计时
                auto iterStart = high_resolution_clock::now();

                auto trainData = trainDataLoader->next();
                auto example = trainData[0];

                // 安全检查
                if (example.first.empty() || example.second.empty()) {
                    cout << "Error: Empty training data at iteration " << i << endl;
                    continue;
                }

                // 前向传播 - 添加安全检查
                auto input = example.first[0];
                if (input.get() == nullptr) {
                    cout << "Error: Null input at iteration " << i << endl;
                    continue;
                }

                auto predict = model->forward(_Convert(input, NC4HW4));
                if (predict.get() == nullptr) {
                    cout << "Error: Model forward failed at iteration " << i << endl;
                    continue;
                }

                    // 检查预测输出的形状
                    auto predictShape = predict->getInfo();
                    if (!predictShape || predictShape->dim.size() < 3) {
                        cout << "Error: Invalid prediction shape at iteration " << i << endl;
                        continue;
                    }

                    int batch = predictShape->dim[0];
                    int seq_len = predictShape->dim[1];
                    int channels = predictShape->dim[2];

                    cout << "Prediction shape: [" << batch << ", " << seq_len << ", " << channels << "]" << endl;

                    // 创建简化的目标标签 - 使用分类损失而不是检测损失
                    // 为了避免复杂的检测损失，我们使用简单的分类任务
                    auto label = example.second[0];
                    if (label.get() == nullptr) {
                        cout << "Error: Null label at iteration " << i << endl;
                        continue;
                    }

                    // 使用简化的分类损失 - 类似EfficientNet
                    auto newTarget = _OneHot(_Cast<int32_t>(_Squeeze(label + _Scalar<int32_t>(addToLabel), {})),
                                            _Scalar<int>(numClasses), _Scalar<float>(1.0f), _Scalar<float>(0.0f));

                    // 将预测结果转换为分类格式 - 取平均池化
                    auto classifyPredict = _ReduceMean(predict, {1}); // 对序列维度求平均
                    classifyPredict = _Convert(classifyPredict, NCHW);

                    // 使用MSE损失函数 (简化版)
                    auto loss = _SquaredDifference(classifyPredict, newTarget);
                    loss = _ReduceMean(loss, {});

                    // 恒定学习率
                    const float constantLearningRate = 0.0001f;
                    solver->setLearningRate(constantLearningRate);

                    // 反向传播和参数更新
                    solver->step(loss);

                    float currentLoss = loss->readMap<float>()[0];
                    epochLoss += currentLoss;

                    // 结束计时
                    auto iterEnd = high_resolution_clock::now();
                    auto iterDuration = duration_cast<milliseconds>(iterEnd - iterStart);

                    // 每个iteration都打印耗时
                    cout << "train iteration: " << solver->currentStep();
                    cout << " batch_size: " << batchSize;
                    cout << " loss: " << currentLoss;
                    cout << " lr: " << constantLearningRate;
                    cout << " time: " << iterDuration.count() << "ms" << endl;

                // 移除异常处理，因为Android编译环境禁用了异常

                // 垃圾回收
                exe->gc(Executor::FULL);
            }
            
            float avgLoss = epochLoss / trainIterations;
            cout << "Epoch " << (epoch + 1) << " completed. Average loss: " << avgLoss << endl;

            // 简单的验证阶段（可选）
            if (testIterations > 0 && testIterations < 50) {
                cout << "Running validation..." << endl;
                testDataLoader->reset();
                model->setIsTraining(false);
                float validationLoss = 0.0f;

                for (int i = 0; i < min(testIterations, 10); i++) { // 最多验证10个样本
                    auto data = testDataLoader->next();
                    auto example = data[0];

                    if (example.first.empty() || example.second.empty()) {
                        continue;
                    }

                    auto predict = model->forward(_Convert(example.first[0], NC4HW4));
                    if (predict.get() == nullptr) {
                        continue;
                    }

                    // 使用简化的分类验证 - 类似训练阶段
                    auto label = example.second[0];
                    if (label.get() == nullptr) {
                        continue;
                    }

                    auto newTarget = _OneHot(_Cast<int32_t>(_Squeeze(label + _Scalar<int32_t>(addToLabel), {})),
                                            _Scalar<int>(numClasses), _Scalar<float>(1.0f), _Scalar<float>(0.0f));

                    auto classifyPredict = predict;
                    if (classifyPredict->getInfo()->dim.size() > 2) {
                        classifyPredict = _ReduceMean(classifyPredict, {1}); // 对序列维度求平均
                    }
                    classifyPredict = _Convert(classifyPredict, NCHW);

                    auto loss = _SquaredDifference(classifyPredict, newTarget);
                    loss = _ReduceMean(loss, {});
                    validationLoss += loss->readMap<float>()[0];
                }
                
                float avgValidationLoss = validationLoss / min(testIterations, 10);
                cout << "Validation loss: " << avgValidationLoss << endl;
            }
        }

        cout << "\n=== YOLOv10 GPU Training Completed ===" << endl;

        // 保存训练后的模型
        cout << "Saving trained model..." << endl;
        char modelPathBuffer[256];
        snprintf(modelPathBuffer, sizeof(modelPathBuffer), "yolov10_trained_batch%d.mnn", batchSize);

        // 使用模块的保存功能
        model->setIsTraining(false);
        Variable::save(model->parameters(), modelPathBuffer);
        cout << "✅ Model saved to: " << modelPathBuffer << endl;
    }
};

// YOLOv10训练Demo类
class YOLOv10TrainingDemo : public DemoUnit {
public:
    virtual int run(int argc, const char* argv[]) override {
        if (argc < 3) {
            std::cout << "usage: ./runTrainDemo.out YOLOv10TrainingDemo path/to/train/images/ path/to/train/image/txt [backend_type] [batch_size]" << std::endl;
            std::cout << "backend_type: 0=CPU, 3=GPU(OpenCL)" << std::endl;
            std::cout << "batch_size: 1,4,8,16,32,64 (default: 1)" << std::endl;
            return 0;
        }

        // 默认使用GPU训练，可通过参数指定
        MNNForwardType type = MNN_FORWARD_OPENCL;
        if (argc >= 4) {
            int backendType = atoi(argv[3]);
            if (backendType == 0) {
                type = MNN_FORWARD_CPU;
            }
        }

        // 解析batch size参数
        int batchSize = 1; // 默认batch size
        if (argc >= 5) {
            int inputBatchSize = atoi(argv[4]);
            // 验证batch size是否在支持的范围内
            std::vector<int> supportedBatchSizes = {1, 4, 8, 16, 32, 64};
            bool isSupported = false;
            for (int size : supportedBatchSizes) {
                if (inputBatchSize == size) {
                    batchSize = inputBatchSize;
                    isSupported = true;
                    break;
                }
            }
            if (!isSupported) {
                std::cout << "Warning: Unsupported batch size " << inputBatchSize
                         << ". Using default batch size 1." << std::endl;
                std::cout << "Supported batch sizes: 1, 4, 8, 16, 32, 64" << std::endl;
            }
        }

        // 设置随机种子
        RandomGenerator::generator(42);

        std::string trainImagesFolder = argv[1];
        std::string trainImagesTxt = argv[2];
        std::string testImagesFolder = argv[1]; // 使用相同的数据进行测试
        std::string testImagesTxt = argv[2];

        // 创建YOLOv10训练模型
        std::shared_ptr<Module> model(new YOLOv10Model(NUM_CLASSES, INPUT_SIZE));

        // 打印模型信息
        static_cast<YOLOv10Model*>(model.get())->printModelInfo();

        // 开始GPU训练（FP32 + 目标检测损失 + 计时 + 可变batch size）
        YOLOv10GPUTrainer::train(type, 1, model, NUM_CLASSES, 0,
                                trainImagesFolder, trainImagesTxt,
                                testImagesFolder, testImagesTxt, INPUT_SIZE, batchSize);

        return 0;
    }
};

DemoUnitSetRegister(YOLOv10TrainingDemo, "YOLOv10TrainingDemo");
