#include <MNN/expr/ExprCreator.hpp>
#include "efficientnet_b1_express.hpp"
static std::vector<MNN::Express::VARP> ConvertTensor1(MNN::Express::VARP x0) {
static const char* ConvertTensor1_main = R"func(
{
    "main_type": "TensorConvertInfo",
    "main": {
        "source": "NCHW",
        "dest": "NC4HW4"
    },
    "type": "ConvertTensor",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvertTensor1_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution2(MNN::Express::VARP x0) {
static const char* Convolution2_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 32,
            "inputCount": 3,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution2_main, 1);
}
static std::vector<MNN::Express::VARP> UnaryOp3(MNN::Express::VARP x0) {
static const char* UnaryOp3_main = R"func(
{
    "main_type": "UnaryOp",
    "main": {
        "opType": "SILU",
        "T": "DT_INVALID"
    },
    "type": "UnaryOp",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, UnaryOp3_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise4(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise4_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 32,
            "outputCount": 32,
            "inputCount": 32,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise4_main, 1);
}
static std::vector<MNN::Express::VARP> ConvertTensor6(MNN::Express::VARP x0) {
static const char* ConvertTensor6_main = R"func(
{
    "main_type": "TensorConvertInfo",
    "main": {
        "source": "NC4HW4",
        "dest": "NCHW"
    },
    "type": "ConvertTensor",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvertTensor6_main, 1);
}
static std::vector<MNN::Express::VARP> Reduction7(MNN::Express::VARP x0) {
static const char* Reduction7_main = R"func(
{
    "main_type": "ReductionParam",
    "main": {
        "operation": "MEAN",
        "dim": [
            2,
            3
        ],
        "coeff": 0.0,
        "keepDims": true,
        "dType": "DT_FLOAT"
    },
    "type": "Reduction",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Reduction7_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution9(MNN::Express::VARP x0) {
static const char* Convolution9_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 8,
            "inputCount": 32,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution9_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution11(MNN::Express::VARP x0) {
static const char* Convolution11_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 32,
            "inputCount": 8,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution11_main, 1);
}
static std::vector<MNN::Express::VARP> UnaryOp12(MNN::Express::VARP x0) {
static const char* UnaryOp12_main = R"func(
{
    "main_type": "UnaryOp",
    "main": {
        "opType": "SIGMOID",
        "T": "DT_INVALID"
    },
    "type": "UnaryOp",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, UnaryOp12_main, 1);
}
static std::vector<MNN::Express::VARP> BinaryOp13(MNN::Express::VARP x0, MNN::Express::VARP x1) {
static const char* BinaryOp13_main = R"func(
{
    "main_type": "BinaryOp",
    "main": {
        "opType": 2,
        "T": "DT_FLOAT",
        "activationType": 0
    },
    "type": "BinaryOp",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0, x1}, BinaryOp13_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution14(MNN::Express::VARP x0) {
static const char* Convolution14_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 16,
            "inputCount": 32,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution14_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise15(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise15_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 16,
            "outputCount": 16,
            "inputCount": 16,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise15_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution20(MNN::Express::VARP x0) {
static const char* Convolution20_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 4,
            "inputCount": 16,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution20_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution22(MNN::Express::VARP x0) {
static const char* Convolution22_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 16,
            "inputCount": 4,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution22_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution25(MNN::Express::VARP x0) {
static const char* Convolution25_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 16,
            "inputCount": 16,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution25_main, 1);
}
static std::vector<MNN::Express::VARP> BinaryOp26(MNN::Express::VARP x0, MNN::Express::VARP x1) {
static const char* BinaryOp26_main = R"func(
{
    "main_type": "BinaryOp",
    "main": {
        "opType": 0,
        "T": "DT_FLOAT",
        "activationType": 0
    },
    "type": "BinaryOp",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0, x1}, BinaryOp26_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution27(MNN::Express::VARP x0) {
static const char* Convolution27_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 96,
            "inputCount": 16,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution27_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise29(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise29_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 96,
            "outputCount": 96,
            "inputCount": 96,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise29_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution34(MNN::Express::VARP x0) {
static const char* Convolution34_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 4,
            "inputCount": 96,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution34_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution36(MNN::Express::VARP x0) {
static const char* Convolution36_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 96,
            "inputCount": 4,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution36_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution39(MNN::Express::VARP x0) {
static const char* Convolution39_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 24,
            "inputCount": 96,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution39_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution40(MNN::Express::VARP x0) {
static const char* Convolution40_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 144,
            "inputCount": 24,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution40_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise42(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise42_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 144,
            "outputCount": 144,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise42_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution47(MNN::Express::VARP x0) {
static const char* Convolution47_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 6,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution47_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution49(MNN::Express::VARP x0) {
static const char* Convolution49_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 144,
            "inputCount": 6,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution49_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution52(MNN::Express::VARP x0) {
static const char* Convolution52_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 24,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution52_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise70(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise70_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 144,
            "outputCount": 144,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise70_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution80(MNN::Express::VARP x0) {
static const char* Convolution80_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 40,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution80_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution81(MNN::Express::VARP x0) {
static const char* Convolution81_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 240,
            "inputCount": 40,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution81_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise83(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise83_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 240,
            "outputCount": 240,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise83_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution88(MNN::Express::VARP x0) {
static const char* Convolution88_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 10,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution88_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution90(MNN::Express::VARP x0) {
static const char* Convolution90_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 240,
            "inputCount": 10,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution90_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution93(MNN::Express::VARP x0) {
static const char* Convolution93_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 40,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution93_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise111(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise111_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 240,
            "outputCount": 240,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise111_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution121(MNN::Express::VARP x0) {
static const char* Convolution121_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 80,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution121_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution122(MNN::Express::VARP x0) {
static const char* Convolution122_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 480,
            "inputCount": 80,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution122_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise124(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise124_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 480,
            "outputCount": 480,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise124_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution129(MNN::Express::VARP x0) {
static const char* Convolution129_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 20,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution129_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution131(MNN::Express::VARP x0) {
static const char* Convolution131_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 480,
            "inputCount": 20,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution131_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution134(MNN::Express::VARP x0) {
static const char* Convolution134_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 80,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution134_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise166(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise166_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 480,
            "outputCount": 480,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise166_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution176(MNN::Express::VARP x0) {
static const char* Convolution176_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 112,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution176_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution177(MNN::Express::VARP x0) {
static const char* Convolution177_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 672,
            "inputCount": 112,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution177_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise179(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise179_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 672,
            "outputCount": 672,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise179_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution184(MNN::Express::VARP x0) {
static const char* Convolution184_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 28,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution184_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution186(MNN::Express::VARP x0) {
static const char* Convolution186_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 672,
            "inputCount": 28,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution186_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution189(MNN::Express::VARP x0) {
static const char* Convolution189_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 112,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution189_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise221(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise221_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 672,
            "outputCount": 672,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise221_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution231(MNN::Express::VARP x0) {
static const char* Convolution231_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 192,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution231_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution232(MNN::Express::VARP x0) {
static const char* Convolution232_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1152,
            "inputCount": 192,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution232_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise234(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise234_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1152,
            "outputCount": 1152,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise234_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution239(MNN::Express::VARP x0) {
static const char* Convolution239_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 48,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution239_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution241(MNN::Express::VARP x0) {
static const char* Convolution241_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1152,
            "inputCount": 48,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution241_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution244(MNN::Express::VARP x0) {
static const char* Convolution244_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 192,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution244_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise290(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise290_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1152,
            "outputCount": 1152,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise290_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution300(MNN::Express::VARP x0) {
static const char* Convolution300_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 320,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution300_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution301(MNN::Express::VARP x0) {
static const char* Convolution301_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1920,
            "inputCount": 320,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution301_main, 1);
}
static std::vector<MNN::Express::VARP> ConvolutionDepthwise303(MNN::Express::VARP x0) {
static const char* ConvolutionDepthwise303_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1920,
            "outputCount": 1920,
            "inputCount": 1920,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, ConvolutionDepthwise303_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution308(MNN::Express::VARP x0) {
static const char* Convolution308_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 80,
            "inputCount": 1920,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution308_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution310(MNN::Express::VARP x0) {
static const char* Convolution310_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1920,
            "inputCount": 80,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution310_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution313(MNN::Express::VARP x0) {
static const char* Convolution313_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 320,
            "inputCount": 1920,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution313_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution315(MNN::Express::VARP x0) {
static const char* Convolution315_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1280,
            "inputCount": 320,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution315_main, 1);
}
static std::vector<MNN::Express::VARP> Pooling3D317(MNN::Express::VARP x0) {
static const char* Pooling3D317_main = R"func(
{
    "main_type": "Pool3D",
    "main": {
        "type": "AVEPOOL",
        "padType": "CAFFE",
        "isGlobal": true
    },
    "type": "Pooling3D",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Pooling3D317_main, 1);
}
static std::vector<MNN::Express::VARP> Flatten318(MNN::Express::VARP x0) {
static const char* Flatten318_main = R"func(
{
    "main_type": "Flatten",
    "main": {
        "axis": 1,
        "endAxis": 0
    },
    "type": "Flatten",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Flatten318_main, 1);
}
static std::vector<MNN::Express::VARP> Const319(MNN::Express::VARP x0) {
static const char* Const319_main = R"func(
{
    "main_type": "Blob",
    "main": {
        "dims": [
            4
        ],
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            -1,
            1280,
            1,
            1
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Const319_main, 1);
}
static std::vector<MNN::Express::VARP> Reshape320(MNN::Express::VARP x0, MNN::Express::VARP x1) {
static const char* Reshape320_main = R"func(
{
    "main_type": "Reshape",
    "main": {
        "dimType": "NCHW"
    },
    "type": "Reshape",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0, x1}, Reshape320_main, 1);
}
static std::vector<MNN::Express::VARP> Convolution321(MNN::Express::VARP x0) {
static const char* Convolution321_main = R"func(
{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1000,
            "inputCount": 1280,
            "relu": false,
            "relu6": false,
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Convolution321_main, 1);
}
static std::vector<MNN::Express::VARP> Shape323(MNN::Express::VARP x0) {
static const char* Shape323_main = R"func(
{
    "main_type": "NONE",
    "type": "Shape",
    "defaultDimentionFormat": "NCHW"
})func";
return _JSONOp({x0}, Shape323_main, 1);
}
static std::vector<MNN::Express::VARP> Const324(MNN::Express::VARP x0) {
static const char* Const324_main = R"func(
{
    "main_type": "Blob",
    "main": {
        "dims": [
            1
        ],
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            0
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Const324_main, 1);
}
static std::vector<MNN::Express::VARP> Rank325(MNN::Express::VARP x0) {
static const char* Rank325_main = R"func(
{
    "main_type": "NONE",
    "type": "Rank",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Rank325_main, 1);
}
static std::vector<MNN::Express::VARP> Const326(MNN::Express::VARP x0) {
static const char* Const326_main = R"func(
{
    "main_type": "Blob",
    "main": {
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            2
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Const326_main, 1);
}
static std::vector<MNN::Express::VARP> BinaryOp327(MNN::Express::VARP x0, MNN::Express::VARP x1) {
static const char* BinaryOp327_main = R"func(
{
    "main_type": "BinaryOp",
    "main": {
        "opType": 1,
        "T": "DT_FLOAT",
        "activationType": 0
    },
    "type": "BinaryOp",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0, x1}, BinaryOp327_main, 1);
}
static std::vector<MNN::Express::VARP> Unsqueeze328(MNN::Express::VARP x0) {
static const char* Unsqueeze328_main = R"func(
{
    "main_type": "SqueezeParam",
    "main": {
        "squeezeDims": [
            0
        ]
    },
    "type": "Unsqueeze",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Unsqueeze328_main, 1);
}
static std::vector<MNN::Express::VARP> SliceTf329(MNN::Express::VARP x0, MNN::Express::VARP x1, MNN::Express::VARP x2) {
static const char* SliceTf329_main = R"func(
{
    "main_type": "NONE",
    "type": "SliceTf",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0, x1, x2}, SliceTf329_main, 1);
}
static std::vector<MNN::Express::VARP> Const330(MNN::Express::VARP x0) {
static const char* Const330_main = R"func(
{
    "main_type": "Blob",
    "main": {
        "dims": [
            1
        ],
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            1
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Const330_main, 1);
}
static std::vector<MNN::Express::VARP> Const332(MNN::Express::VARP x0) {
static const char* Const332_main = R"func(
{
    "main_type": "Blob",
    "main": {
        "dims": [
            1
        ],
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            1000
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0}, Const332_main, 1);
}
static std::vector<MNN::Express::VARP> Concat333(MNN::Express::VARP x0, MNN::Express::VARP x1, MNN::Express::VARP x2) {
static const char* Concat333_main = R"func(
{
    "main_type": "Axis",
    "main": {
        "axis": 0
    },
    "type": "Concat",
    "defaultDimentionFormat": "NHWC"
})func";
return _JSONOp({x0, x1, x2}, Concat333_main, 1);
}
std::map<std::string, MNN::Express::VARP> efficientnet_b1_express(const std::map<std::string, MNN::Express::VARP>& ____inputs) {
std::vector<MNN::Express::VARP> t(335);
// Init Inputs
t[0] = ____inputs.find("input")->second;
// Call Funciton

{
MNN::Express::VARPS tmp = ConvertTensor1(t[0]);
t[1] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution2(t[1]);
t[2] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[2]);
t[3] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise4(t[3]);
t[4] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[4]);
t[5] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[5]);
t[6] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[6]);
t[7] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[7]);
t[8] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution9(t[8]);
t[9] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[9]);
t[10] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution11(t[10]);
t[11] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[11]);
t[12] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[5],t[12]);
t[13] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution14(t[13]);
t[14] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise15(t[14]);
t[15] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[15]);
t[16] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[16]);
t[17] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[17]);
t[18] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[18]);
t[19] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution20(t[19]);
t[20] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[20]);
t[21] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution22(t[21]);
t[22] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[22]);
t[23] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[16],t[23]);
t[24] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution25(t[24]);
t[25] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[25],t[14]);
t[26] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution27(t[26]);
t[27] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[27]);
t[28] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise29(t[28]);
t[29] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[29]);
t[30] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[30]);
t[31] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[31]);
t[32] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[32]);
t[33] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution34(t[33]);
t[34] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[34]);
t[35] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution36(t[35]);
t[36] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[36]);
t[37] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[30],t[37]);
t[38] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution39(t[38]);
t[39] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution40(t[39]);
t[40] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[40]);
t[41] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise42(t[41]);
t[42] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[42]);
t[43] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[43]);
t[44] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[44]);
t[45] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[45]);
t[46] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution47(t[46]);
t[47] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[47]);
t[48] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution49(t[48]);
t[49] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[49]);
t[50] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[43],t[50]);
t[51] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution52(t[51]);
t[52] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[52],t[39]);
t[53] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution40(t[53]);
t[54] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[54]);
t[55] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise42(t[55]);
t[56] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[56]);
t[57] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[57]);
t[58] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[58]);
t[59] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[59]);
t[60] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution47(t[60]);
t[61] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[61]);
t[62] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution49(t[62]);
t[63] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[63]);
t[64] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[57],t[64]);
t[65] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution52(t[65]);
t[66] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[66],t[53]);
t[67] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution40(t[67]);
t[68] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[68]);
t[69] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise70(t[69]);
t[70] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[70]);
t[71] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[71]);
t[72] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[72]);
t[73] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[73]);
t[74] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution47(t[74]);
t[75] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[75]);
t[76] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution49(t[76]);
t[77] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[77]);
t[78] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[71],t[78]);
t[79] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution80(t[79]);
t[80] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution81(t[80]);
t[81] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[81]);
t[82] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise83(t[82]);
t[83] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[83]);
t[84] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[84]);
t[85] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[85]);
t[86] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[86]);
t[87] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution88(t[87]);
t[88] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[88]);
t[89] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution90(t[89]);
t[90] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[90]);
t[91] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[84],t[91]);
t[92] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution93(t[92]);
t[93] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[93],t[80]);
t[94] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution81(t[94]);
t[95] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[95]);
t[96] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise83(t[96]);
t[97] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[97]);
t[98] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[98]);
t[99] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[99]);
t[100] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[100]);
t[101] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution88(t[101]);
t[102] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[102]);
t[103] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution90(t[103]);
t[104] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[104]);
t[105] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[98],t[105]);
t[106] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution93(t[106]);
t[107] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[107],t[94]);
t[108] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution81(t[108]);
t[109] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[109]);
t[110] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise111(t[110]);
t[111] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[111]);
t[112] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[112]);
t[113] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[113]);
t[114] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[114]);
t[115] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution88(t[115]);
t[116] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[116]);
t[117] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution90(t[117]);
t[118] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[118]);
t[119] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[112],t[119]);
t[120] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution121(t[120]);
t[121] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution122(t[121]);
t[122] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[122]);
t[123] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise124(t[123]);
t[124] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[124]);
t[125] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[125]);
t[126] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[126]);
t[127] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[127]);
t[128] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution129(t[128]);
t[129] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[129]);
t[130] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution131(t[130]);
t[131] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[131]);
t[132] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[125],t[132]);
t[133] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution134(t[133]);
t[134] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[134],t[121]);
t[135] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution122(t[135]);
t[136] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[136]);
t[137] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise124(t[137]);
t[138] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[138]);
t[139] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[139]);
t[140] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[140]);
t[141] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[141]);
t[142] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution129(t[142]);
t[143] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[143]);
t[144] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution131(t[144]);
t[145] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[145]);
t[146] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[139],t[146]);
t[147] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution134(t[147]);
t[148] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[148],t[135]);
t[149] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution122(t[149]);
t[150] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[150]);
t[151] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise124(t[151]);
t[152] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[152]);
t[153] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[153]);
t[154] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[154]);
t[155] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[155]);
t[156] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution129(t[156]);
t[157] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[157]);
t[158] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution131(t[158]);
t[159] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[159]);
t[160] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[153],t[160]);
t[161] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution134(t[161]);
t[162] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[162],t[149]);
t[163] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution122(t[163]);
t[164] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[164]);
t[165] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise166(t[165]);
t[166] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[166]);
t[167] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[167]);
t[168] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[168]);
t[169] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[169]);
t[170] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution129(t[170]);
t[171] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[171]);
t[172] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution131(t[172]);
t[173] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[173]);
t[174] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[167],t[174]);
t[175] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution176(t[175]);
t[176] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution177(t[176]);
t[177] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[177]);
t[178] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise179(t[178]);
t[179] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[179]);
t[180] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[180]);
t[181] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[181]);
t[182] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[182]);
t[183] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution184(t[183]);
t[184] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[184]);
t[185] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution186(t[185]);
t[186] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[186]);
t[187] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[180],t[187]);
t[188] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution189(t[188]);
t[189] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[189],t[176]);
t[190] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution177(t[190]);
t[191] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[191]);
t[192] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise179(t[192]);
t[193] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[193]);
t[194] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[194]);
t[195] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[195]);
t[196] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[196]);
t[197] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution184(t[197]);
t[198] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[198]);
t[199] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution186(t[199]);
t[200] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[200]);
t[201] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[194],t[201]);
t[202] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution189(t[202]);
t[203] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[203],t[190]);
t[204] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution177(t[204]);
t[205] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[205]);
t[206] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise179(t[206]);
t[207] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[207]);
t[208] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[208]);
t[209] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[209]);
t[210] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[210]);
t[211] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution184(t[211]);
t[212] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[212]);
t[213] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution186(t[213]);
t[214] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[214]);
t[215] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[208],t[215]);
t[216] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution189(t[216]);
t[217] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[217],t[204]);
t[218] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution177(t[218]);
t[219] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[219]);
t[220] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise221(t[220]);
t[221] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[221]);
t[222] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[222]);
t[223] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[223]);
t[224] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[224]);
t[225] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution184(t[225]);
t[226] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[226]);
t[227] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution186(t[227]);
t[228] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[228]);
t[229] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[222],t[229]);
t[230] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution231(t[230]);
t[231] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution232(t[231]);
t[232] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[232]);
t[233] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise234(t[233]);
t[234] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[234]);
t[235] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[235]);
t[236] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[236]);
t[237] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[237]);
t[238] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution239(t[238]);
t[239] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[239]);
t[240] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution241(t[240]);
t[241] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[241]);
t[242] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[235],t[242]);
t[243] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution244(t[243]);
t[244] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[244],t[231]);
t[245] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution232(t[245]);
t[246] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[246]);
t[247] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise234(t[247]);
t[248] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[248]);
t[249] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[249]);
t[250] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[250]);
t[251] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[251]);
t[252] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution239(t[252]);
t[253] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[253]);
t[254] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution241(t[254]);
t[255] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[255]);
t[256] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[249],t[256]);
t[257] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution244(t[257]);
t[258] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[258],t[245]);
t[259] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution232(t[259]);
t[260] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[260]);
t[261] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise234(t[261]);
t[262] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[262]);
t[263] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[263]);
t[264] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[264]);
t[265] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[265]);
t[266] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution239(t[266]);
t[267] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[267]);
t[268] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution241(t[268]);
t[269] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[269]);
t[270] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[263],t[270]);
t[271] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution244(t[271]);
t[272] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[272],t[259]);
t[273] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution232(t[273]);
t[274] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[274]);
t[275] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise234(t[275]);
t[276] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[276]);
t[277] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[277]);
t[278] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[278]);
t[279] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[279]);
t[280] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution239(t[280]);
t[281] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[281]);
t[282] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution241(t[282]);
t[283] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[283]);
t[284] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[277],t[284]);
t[285] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution244(t[285]);
t[286] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[286],t[273]);
t[287] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution232(t[287]);
t[288] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[288]);
t[289] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise290(t[289]);
t[290] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[290]);
t[291] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[291]);
t[292] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[292]);
t[293] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[293]);
t[294] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution239(t[294]);
t[295] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[295]);
t[296] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution241(t[296]);
t[297] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[297]);
t[298] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[291],t[298]);
t[299] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution300(t[299]);
t[300] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution301(t[300]);
t[301] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[301]);
t[302] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvolutionDepthwise303(t[302]);
t[303] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[303]);
t[304] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[304]);
t[305] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reduction7(t[305]);
t[306] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor1(t[306]);
t[307] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution308(t[307]);
t[308] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[308]);
t[309] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution310(t[309]);
t[310] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp12(t[310]);
t[311] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp13(t[304],t[311]);
t[312] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution313(t[312]);
t[313] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp26(t[313],t[300]);
t[314] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution315(t[314]);
t[315] = tmp[0];
}

{
MNN::Express::VARPS tmp = UnaryOp3(t[315]);
t[316] = tmp[0];
}

{
MNN::Express::VARPS tmp = Pooling3D317(t[316]);
t[317] = tmp[0];
}

{
MNN::Express::VARPS tmp = Flatten318(t[317]);
t[318] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reshape320(t[318],t[319]);
t[320] = tmp[0];
}

{
MNN::Express::VARPS tmp = Convolution321(t[320]);
t[321] = tmp[0];
}

{
MNN::Express::VARPS tmp = ConvertTensor6(t[321]);
t[322] = tmp[0];
}

{
MNN::Express::VARPS tmp = Shape323(t[318]);
t[323] = tmp[0];
}

{
MNN::Express::VARPS tmp = Rank325(t[318]);
t[325] = tmp[0];
}

{
MNN::Express::VARPS tmp = BinaryOp327(t[325],t[326]);
t[327] = tmp[0];
}

{
MNN::Express::VARPS tmp = Unsqueeze328(t[327]);
t[328] = tmp[0];
}

{
MNN::Express::VARPS tmp = SliceTf329(t[323],t[324],t[328]);
t[329] = tmp[0];
}

{
MNN::Express::VARPS tmp = SliceTf329(t[323],t[328],t[330]);
t[331] = tmp[0];
}

{
MNN::Express::VARPS tmp = Concat333(t[329],t[331],t[332]);
t[333] = tmp[0];
}

{
MNN::Express::VARPS tmp = Reshape320(t[322],t[333]);
t[334] = tmp[0];
}
// Collect Outputs
std::map<std::string, MNN::Express::VARP> _____outputs;
t[334] ->setName("logits");
_____outputs["logits"] = t[334];
return _____outputs;
}
