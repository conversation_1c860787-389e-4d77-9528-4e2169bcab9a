import MNN.expr as F
def efficientnet_b1_express(input):
    ConvertTensor1 = """{
    "main_type": "TensorConvertInfo",
    "main": {
        "source": "NCHW",
        "dest": "NC4HW4"
    },
    "type": "ConvertTensor",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution2 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 32,
            "inputCount": 3,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    UnaryOp3 = """{
    "main_type": "UnaryOp",
    "main": {
        "opType": "SILU",
        "T": "DT_INVALID"
    },
    "type": "UnaryOp",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise4 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 32,
            "outputCount": 32,
            "inputCount": 32,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvertTensor6 = """{
    "main_type": "TensorConvertInfo",
    "main": {
        "source": "NC4HW4",
        "dest": "NCHW"
    },
    "type": "ConvertTensor",
    "defaultDimentionFormat": "NHWC"
}    """
    Reduction7 = """{
    "main_type": "ReductionParam",
    "main": {
        "operation": "MEAN",
        "dim": [
            2,
            3
        ],
        "coeff": 0.0,
        "keepDims": true,
        "dType": "DT_FLOAT"
    },
    "type": "Reduction",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution9 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 8,
            "inputCount": 32,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution11 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 32,
            "inputCount": 8,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    UnaryOp12 = """{
    "main_type": "UnaryOp",
    "main": {
        "opType": "SIGMOID",
        "T": "DT_INVALID"
    },
    "type": "UnaryOp",
    "defaultDimentionFormat": "NHWC"
}    """
    BinaryOp13 = """{
    "main_type": "BinaryOp",
    "main": {
        "opType": 2,
        "T": "DT_FLOAT",
        "activationType": 0
    },
    "type": "BinaryOp",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution14 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 16,
            "inputCount": 32,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise15 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 16,
            "outputCount": 16,
            "inputCount": 16,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution20 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 4,
            "inputCount": 16,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution22 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 16,
            "inputCount": 4,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution25 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 16,
            "inputCount": 16,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    BinaryOp26 = """{
    "main_type": "BinaryOp",
    "main": {
        "opType": 0,
        "T": "DT_FLOAT",
        "activationType": 0
    },
    "type": "BinaryOp",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution27 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 96,
            "inputCount": 16,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise29 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 96,
            "outputCount": 96,
            "inputCount": 96,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution34 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 4,
            "inputCount": 96,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution36 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 96,
            "inputCount": 4,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution39 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 24,
            "inputCount": 96,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution40 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 144,
            "inputCount": 24,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise42 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 144,
            "outputCount": 144,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution47 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 6,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution49 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 144,
            "inputCount": 6,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution52 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 24,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise70 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 144,
            "outputCount": 144,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution80 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 40,
            "inputCount": 144,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution81 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 240,
            "inputCount": 40,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise83 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 240,
            "outputCount": 240,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution88 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 10,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution90 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 240,
            "inputCount": 10,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution93 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 40,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise111 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 240,
            "outputCount": 240,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution121 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 80,
            "inputCount": 240,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution122 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 480,
            "inputCount": 80,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise124 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 480,
            "outputCount": 480,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution129 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 20,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution131 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 480,
            "inputCount": 20,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution134 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 80,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise166 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 480,
            "outputCount": 480,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution176 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 112,
            "inputCount": 480,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution177 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 672,
            "inputCount": 112,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise179 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 672,
            "outputCount": 672,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution184 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 28,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution186 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 672,
            "inputCount": 28,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution189 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 112,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise221 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 2,
            "strideY": 2,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 672,
            "outputCount": 672,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution231 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 192,
            "inputCount": 672,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution232 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1152,
            "inputCount": 192,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise234 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 2,
            "padY": 2,
            "kernelX": 5,
            "kernelY": 5,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1152,
            "outputCount": 1152,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                2,
                2,
                2,
                2
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution239 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 48,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution241 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1152,
            "inputCount": 48,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution244 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 192,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise290 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1152,
            "outputCount": 1152,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution300 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 320,
            "inputCount": 1152,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution301 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1920,
            "inputCount": 320,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    ConvolutionDepthwise303 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 1,
            "padY": 1,
            "kernelX": 3,
            "kernelY": 3,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1920,
            "outputCount": 1920,
            "inputCount": 1920,
            "relu": false,
            "relu6": false,
            "pads": [
                1,
                1,
                1,
                1
            ],
            "hasOutputShape": false
        }
    },
    "type": "ConvolutionDepthwise",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution308 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 80,
            "inputCount": 1920,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution310 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1920,
            "inputCount": 80,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution313 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 320,
            "inputCount": 1920,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution315 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1280,
            "inputCount": 320,
            "relu": false,
            "relu6": false,
            "pads": [
                0,
                0,
                0,
                0
            ],
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Pooling3D317 = """{
    "main_type": "Pool3D",
    "main": {
        "type": "AVEPOOL",
        "padType": "CAFFE",
        "isGlobal": true
    },
    "type": "Pooling3D",
    "defaultDimentionFormat": "NHWC"
}    """
    Flatten318 = """{
    "main_type": "Flatten",
    "main": {
        "axis": 1,
        "endAxis": 0
    },
    "type": "Flatten",
    "defaultDimentionFormat": "NHWC"
}    """
    Const319 = """{
    "main_type": "Blob",
    "main": {
        "dims": [
            4
        ],
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            -1,
            1280,
            1,
            1
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
}    """
    Reshape320 = """{
    "main_type": "Reshape",
    "main": {
        "dimType": "NCHW"
    },
    "type": "Reshape",
    "defaultDimentionFormat": "NHWC"
}    """
    Convolution321 = """{
    "main_type": "Convolution2D",
    "main": {
        "common": {
            "padX": 0,
            "padY": 0,
            "kernelX": 1,
            "kernelY": 1,
            "strideX": 1,
            "strideY": 1,
            "dilateX": 1,
            "dilateY": 1,
            "padMode": "CAFFE",
            "group": 1,
            "outputCount": 1000,
            "inputCount": 1280,
            "relu": false,
            "relu6": false,
            "hasOutputShape": false
        }
    },
    "type": "Convolution",
    "defaultDimentionFormat": "NHWC"
}    """
    Shape323 = """{
    "main_type": "NONE",
    "type": "Shape",
    "defaultDimentionFormat": "NCHW"
}    """
    Const324 = """{
    "main_type": "Blob",
    "main": {
        "dims": [
            1
        ],
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            0
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
}    """
    Rank325 = """{
    "main_type": "NONE",
    "type": "Rank",
    "defaultDimentionFormat": "NHWC"
}    """
    Const326 = """{
    "main_type": "Blob",
    "main": {
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            2
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
}    """
    BinaryOp327 = """{
    "main_type": "BinaryOp",
    "main": {
        "opType": 1,
        "T": "DT_FLOAT",
        "activationType": 0
    },
    "type": "BinaryOp",
    "defaultDimentionFormat": "NHWC"
}    """
    Unsqueeze328 = """{
    "main_type": "SqueezeParam",
    "main": {
        "squeezeDims": [
            0
        ]
    },
    "type": "Unsqueeze",
    "defaultDimentionFormat": "NHWC"
}    """
    SliceTf329 = """{
    "main_type": "NONE",
    "type": "SliceTf",
    "defaultDimentionFormat": "NHWC"
}    """
    Const330 = """{
    "main_type": "Blob",
    "main": {
        "dims": [
            1
        ],
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            1
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
}    """
    Const332 = """{
    "main_type": "Blob",
    "main": {
        "dims": [
            1
        ],
        "dataFormat": "NCHW",
        "dataType": "DT_INT32",
        "int32s": [
            1000
        ]
    },
    "type": "Const",
    "defaultDimentionFormat": "NHWC"
}    """
    Concat333 = """{
    "main_type": "Axis",
    "main": {
        "axis": 0
    },
    "type": "Concat",
    "defaultDimentionFormat": "NHWC"
}    """
    stackes = {}
    stackes[0] = input
    # Call Funciton
    p = F.jsonop([stackes[0]], ConvertTensor1, 1)
    stackes[1] = p[0]
    p = F.jsonop([stackes[1]], Convolution2, 1)
    stackes[2] = p[0]
    p = F.jsonop([stackes[2]], UnaryOp3, 1)
    stackes[3] = p[0]
    p = F.jsonop([stackes[3]], ConvolutionDepthwise4, 1)
    stackes[4] = p[0]
    p = F.jsonop([stackes[4]], UnaryOp3, 1)
    stackes[5] = p[0]
    p = F.jsonop([stackes[5]], ConvertTensor6, 1)
    stackes[6] = p[0]
    p = F.jsonop([stackes[6]], Reduction7, 1)
    stackes[7] = p[0]
    p = F.jsonop([stackes[7]], ConvertTensor1, 1)
    stackes[8] = p[0]
    p = F.jsonop([stackes[8]], Convolution9, 1)
    stackes[9] = p[0]
    p = F.jsonop([stackes[9]], UnaryOp3, 1)
    stackes[10] = p[0]
    p = F.jsonop([stackes[10]], Convolution11, 1)
    stackes[11] = p[0]
    p = F.jsonop([stackes[11]], UnaryOp12, 1)
    stackes[12] = p[0]
    p = F.jsonop([stackes[5], stackes[12]], BinaryOp13, 1)
    stackes[13] = p[0]
    p = F.jsonop([stackes[13]], Convolution14, 1)
    stackes[14] = p[0]
    p = F.jsonop([stackes[14]], ConvolutionDepthwise15, 1)
    stackes[15] = p[0]
    p = F.jsonop([stackes[15]], UnaryOp3, 1)
    stackes[16] = p[0]
    p = F.jsonop([stackes[16]], ConvertTensor6, 1)
    stackes[17] = p[0]
    p = F.jsonop([stackes[17]], Reduction7, 1)
    stackes[18] = p[0]
    p = F.jsonop([stackes[18]], ConvertTensor1, 1)
    stackes[19] = p[0]
    p = F.jsonop([stackes[19]], Convolution20, 1)
    stackes[20] = p[0]
    p = F.jsonop([stackes[20]], UnaryOp3, 1)
    stackes[21] = p[0]
    p = F.jsonop([stackes[21]], Convolution22, 1)
    stackes[22] = p[0]
    p = F.jsonop([stackes[22]], UnaryOp12, 1)
    stackes[23] = p[0]
    p = F.jsonop([stackes[16], stackes[23]], BinaryOp13, 1)
    stackes[24] = p[0]
    p = F.jsonop([stackes[24]], Convolution25, 1)
    stackes[25] = p[0]
    p = F.jsonop([stackes[25], stackes[14]], BinaryOp26, 1)
    stackes[26] = p[0]
    p = F.jsonop([stackes[26]], Convolution27, 1)
    stackes[27] = p[0]
    p = F.jsonop([stackes[27]], UnaryOp3, 1)
    stackes[28] = p[0]
    p = F.jsonop([stackes[28]], ConvolutionDepthwise29, 1)
    stackes[29] = p[0]
    p = F.jsonop([stackes[29]], UnaryOp3, 1)
    stackes[30] = p[0]
    p = F.jsonop([stackes[30]], ConvertTensor6, 1)
    stackes[31] = p[0]
    p = F.jsonop([stackes[31]], Reduction7, 1)
    stackes[32] = p[0]
    p = F.jsonop([stackes[32]], ConvertTensor1, 1)
    stackes[33] = p[0]
    p = F.jsonop([stackes[33]], Convolution34, 1)
    stackes[34] = p[0]
    p = F.jsonop([stackes[34]], UnaryOp3, 1)
    stackes[35] = p[0]
    p = F.jsonop([stackes[35]], Convolution36, 1)
    stackes[36] = p[0]
    p = F.jsonop([stackes[36]], UnaryOp12, 1)
    stackes[37] = p[0]
    p = F.jsonop([stackes[30], stackes[37]], BinaryOp13, 1)
    stackes[38] = p[0]
    p = F.jsonop([stackes[38]], Convolution39, 1)
    stackes[39] = p[0]
    p = F.jsonop([stackes[39]], Convolution40, 1)
    stackes[40] = p[0]
    p = F.jsonop([stackes[40]], UnaryOp3, 1)
    stackes[41] = p[0]
    p = F.jsonop([stackes[41]], ConvolutionDepthwise42, 1)
    stackes[42] = p[0]
    p = F.jsonop([stackes[42]], UnaryOp3, 1)
    stackes[43] = p[0]
    p = F.jsonop([stackes[43]], ConvertTensor6, 1)
    stackes[44] = p[0]
    p = F.jsonop([stackes[44]], Reduction7, 1)
    stackes[45] = p[0]
    p = F.jsonop([stackes[45]], ConvertTensor1, 1)
    stackes[46] = p[0]
    p = F.jsonop([stackes[46]], Convolution47, 1)
    stackes[47] = p[0]
    p = F.jsonop([stackes[47]], UnaryOp3, 1)
    stackes[48] = p[0]
    p = F.jsonop([stackes[48]], Convolution49, 1)
    stackes[49] = p[0]
    p = F.jsonop([stackes[49]], UnaryOp12, 1)
    stackes[50] = p[0]
    p = F.jsonop([stackes[43], stackes[50]], BinaryOp13, 1)
    stackes[51] = p[0]
    p = F.jsonop([stackes[51]], Convolution52, 1)
    stackes[52] = p[0]
    p = F.jsonop([stackes[52], stackes[39]], BinaryOp26, 1)
    stackes[53] = p[0]
    p = F.jsonop([stackes[53]], Convolution40, 1)
    stackes[54] = p[0]
    p = F.jsonop([stackes[54]], UnaryOp3, 1)
    stackes[55] = p[0]
    p = F.jsonop([stackes[55]], ConvolutionDepthwise42, 1)
    stackes[56] = p[0]
    p = F.jsonop([stackes[56]], UnaryOp3, 1)
    stackes[57] = p[0]
    p = F.jsonop([stackes[57]], ConvertTensor6, 1)
    stackes[58] = p[0]
    p = F.jsonop([stackes[58]], Reduction7, 1)
    stackes[59] = p[0]
    p = F.jsonop([stackes[59]], ConvertTensor1, 1)
    stackes[60] = p[0]
    p = F.jsonop([stackes[60]], Convolution47, 1)
    stackes[61] = p[0]
    p = F.jsonop([stackes[61]], UnaryOp3, 1)
    stackes[62] = p[0]
    p = F.jsonop([stackes[62]], Convolution49, 1)
    stackes[63] = p[0]
    p = F.jsonop([stackes[63]], UnaryOp12, 1)
    stackes[64] = p[0]
    p = F.jsonop([stackes[57], stackes[64]], BinaryOp13, 1)
    stackes[65] = p[0]
    p = F.jsonop([stackes[65]], Convolution52, 1)
    stackes[66] = p[0]
    p = F.jsonop([stackes[66], stackes[53]], BinaryOp26, 1)
    stackes[67] = p[0]
    p = F.jsonop([stackes[67]], Convolution40, 1)
    stackes[68] = p[0]
    p = F.jsonop([stackes[68]], UnaryOp3, 1)
    stackes[69] = p[0]
    p = F.jsonop([stackes[69]], ConvolutionDepthwise70, 1)
    stackes[70] = p[0]
    p = F.jsonop([stackes[70]], UnaryOp3, 1)
    stackes[71] = p[0]
    p = F.jsonop([stackes[71]], ConvertTensor6, 1)
    stackes[72] = p[0]
    p = F.jsonop([stackes[72]], Reduction7, 1)
    stackes[73] = p[0]
    p = F.jsonop([stackes[73]], ConvertTensor1, 1)
    stackes[74] = p[0]
    p = F.jsonop([stackes[74]], Convolution47, 1)
    stackes[75] = p[0]
    p = F.jsonop([stackes[75]], UnaryOp3, 1)
    stackes[76] = p[0]
    p = F.jsonop([stackes[76]], Convolution49, 1)
    stackes[77] = p[0]
    p = F.jsonop([stackes[77]], UnaryOp12, 1)
    stackes[78] = p[0]
    p = F.jsonop([stackes[71], stackes[78]], BinaryOp13, 1)
    stackes[79] = p[0]
    p = F.jsonop([stackes[79]], Convolution80, 1)
    stackes[80] = p[0]
    p = F.jsonop([stackes[80]], Convolution81, 1)
    stackes[81] = p[0]
    p = F.jsonop([stackes[81]], UnaryOp3, 1)
    stackes[82] = p[0]
    p = F.jsonop([stackes[82]], ConvolutionDepthwise83, 1)
    stackes[83] = p[0]
    p = F.jsonop([stackes[83]], UnaryOp3, 1)
    stackes[84] = p[0]
    p = F.jsonop([stackes[84]], ConvertTensor6, 1)
    stackes[85] = p[0]
    p = F.jsonop([stackes[85]], Reduction7, 1)
    stackes[86] = p[0]
    p = F.jsonop([stackes[86]], ConvertTensor1, 1)
    stackes[87] = p[0]
    p = F.jsonop([stackes[87]], Convolution88, 1)
    stackes[88] = p[0]
    p = F.jsonop([stackes[88]], UnaryOp3, 1)
    stackes[89] = p[0]
    p = F.jsonop([stackes[89]], Convolution90, 1)
    stackes[90] = p[0]
    p = F.jsonop([stackes[90]], UnaryOp12, 1)
    stackes[91] = p[0]
    p = F.jsonop([stackes[84], stackes[91]], BinaryOp13, 1)
    stackes[92] = p[0]
    p = F.jsonop([stackes[92]], Convolution93, 1)
    stackes[93] = p[0]
    p = F.jsonop([stackes[93], stackes[80]], BinaryOp26, 1)
    stackes[94] = p[0]
    p = F.jsonop([stackes[94]], Convolution81, 1)
    stackes[95] = p[0]
    p = F.jsonop([stackes[95]], UnaryOp3, 1)
    stackes[96] = p[0]
    p = F.jsonop([stackes[96]], ConvolutionDepthwise83, 1)
    stackes[97] = p[0]
    p = F.jsonop([stackes[97]], UnaryOp3, 1)
    stackes[98] = p[0]
    p = F.jsonop([stackes[98]], ConvertTensor6, 1)
    stackes[99] = p[0]
    p = F.jsonop([stackes[99]], Reduction7, 1)
    stackes[100] = p[0]
    p = F.jsonop([stackes[100]], ConvertTensor1, 1)
    stackes[101] = p[0]
    p = F.jsonop([stackes[101]], Convolution88, 1)
    stackes[102] = p[0]
    p = F.jsonop([stackes[102]], UnaryOp3, 1)
    stackes[103] = p[0]
    p = F.jsonop([stackes[103]], Convolution90, 1)
    stackes[104] = p[0]
    p = F.jsonop([stackes[104]], UnaryOp12, 1)
    stackes[105] = p[0]
    p = F.jsonop([stackes[98], stackes[105]], BinaryOp13, 1)
    stackes[106] = p[0]
    p = F.jsonop([stackes[106]], Convolution93, 1)
    stackes[107] = p[0]
    p = F.jsonop([stackes[107], stackes[94]], BinaryOp26, 1)
    stackes[108] = p[0]
    p = F.jsonop([stackes[108]], Convolution81, 1)
    stackes[109] = p[0]
    p = F.jsonop([stackes[109]], UnaryOp3, 1)
    stackes[110] = p[0]
    p = F.jsonop([stackes[110]], ConvolutionDepthwise111, 1)
    stackes[111] = p[0]
    p = F.jsonop([stackes[111]], UnaryOp3, 1)
    stackes[112] = p[0]
    p = F.jsonop([stackes[112]], ConvertTensor6, 1)
    stackes[113] = p[0]
    p = F.jsonop([stackes[113]], Reduction7, 1)
    stackes[114] = p[0]
    p = F.jsonop([stackes[114]], ConvertTensor1, 1)
    stackes[115] = p[0]
    p = F.jsonop([stackes[115]], Convolution88, 1)
    stackes[116] = p[0]
    p = F.jsonop([stackes[116]], UnaryOp3, 1)
    stackes[117] = p[0]
    p = F.jsonop([stackes[117]], Convolution90, 1)
    stackes[118] = p[0]
    p = F.jsonop([stackes[118]], UnaryOp12, 1)
    stackes[119] = p[0]
    p = F.jsonop([stackes[112], stackes[119]], BinaryOp13, 1)
    stackes[120] = p[0]
    p = F.jsonop([stackes[120]], Convolution121, 1)
    stackes[121] = p[0]
    p = F.jsonop([stackes[121]], Convolution122, 1)
    stackes[122] = p[0]
    p = F.jsonop([stackes[122]], UnaryOp3, 1)
    stackes[123] = p[0]
    p = F.jsonop([stackes[123]], ConvolutionDepthwise124, 1)
    stackes[124] = p[0]
    p = F.jsonop([stackes[124]], UnaryOp3, 1)
    stackes[125] = p[0]
    p = F.jsonop([stackes[125]], ConvertTensor6, 1)
    stackes[126] = p[0]
    p = F.jsonop([stackes[126]], Reduction7, 1)
    stackes[127] = p[0]
    p = F.jsonop([stackes[127]], ConvertTensor1, 1)
    stackes[128] = p[0]
    p = F.jsonop([stackes[128]], Convolution129, 1)
    stackes[129] = p[0]
    p = F.jsonop([stackes[129]], UnaryOp3, 1)
    stackes[130] = p[0]
    p = F.jsonop([stackes[130]], Convolution131, 1)
    stackes[131] = p[0]
    p = F.jsonop([stackes[131]], UnaryOp12, 1)
    stackes[132] = p[0]
    p = F.jsonop([stackes[125], stackes[132]], BinaryOp13, 1)
    stackes[133] = p[0]
    p = F.jsonop([stackes[133]], Convolution134, 1)
    stackes[134] = p[0]
    p = F.jsonop([stackes[134], stackes[121]], BinaryOp26, 1)
    stackes[135] = p[0]
    p = F.jsonop([stackes[135]], Convolution122, 1)
    stackes[136] = p[0]
    p = F.jsonop([stackes[136]], UnaryOp3, 1)
    stackes[137] = p[0]
    p = F.jsonop([stackes[137]], ConvolutionDepthwise124, 1)
    stackes[138] = p[0]
    p = F.jsonop([stackes[138]], UnaryOp3, 1)
    stackes[139] = p[0]
    p = F.jsonop([stackes[139]], ConvertTensor6, 1)
    stackes[140] = p[0]
    p = F.jsonop([stackes[140]], Reduction7, 1)
    stackes[141] = p[0]
    p = F.jsonop([stackes[141]], ConvertTensor1, 1)
    stackes[142] = p[0]
    p = F.jsonop([stackes[142]], Convolution129, 1)
    stackes[143] = p[0]
    p = F.jsonop([stackes[143]], UnaryOp3, 1)
    stackes[144] = p[0]
    p = F.jsonop([stackes[144]], Convolution131, 1)
    stackes[145] = p[0]
    p = F.jsonop([stackes[145]], UnaryOp12, 1)
    stackes[146] = p[0]
    p = F.jsonop([stackes[139], stackes[146]], BinaryOp13, 1)
    stackes[147] = p[0]
    p = F.jsonop([stackes[147]], Convolution134, 1)
    stackes[148] = p[0]
    p = F.jsonop([stackes[148], stackes[135]], BinaryOp26, 1)
    stackes[149] = p[0]
    p = F.jsonop([stackes[149]], Convolution122, 1)
    stackes[150] = p[0]
    p = F.jsonop([stackes[150]], UnaryOp3, 1)
    stackes[151] = p[0]
    p = F.jsonop([stackes[151]], ConvolutionDepthwise124, 1)
    stackes[152] = p[0]
    p = F.jsonop([stackes[152]], UnaryOp3, 1)
    stackes[153] = p[0]
    p = F.jsonop([stackes[153]], ConvertTensor6, 1)
    stackes[154] = p[0]
    p = F.jsonop([stackes[154]], Reduction7, 1)
    stackes[155] = p[0]
    p = F.jsonop([stackes[155]], ConvertTensor1, 1)
    stackes[156] = p[0]
    p = F.jsonop([stackes[156]], Convolution129, 1)
    stackes[157] = p[0]
    p = F.jsonop([stackes[157]], UnaryOp3, 1)
    stackes[158] = p[0]
    p = F.jsonop([stackes[158]], Convolution131, 1)
    stackes[159] = p[0]
    p = F.jsonop([stackes[159]], UnaryOp12, 1)
    stackes[160] = p[0]
    p = F.jsonop([stackes[153], stackes[160]], BinaryOp13, 1)
    stackes[161] = p[0]
    p = F.jsonop([stackes[161]], Convolution134, 1)
    stackes[162] = p[0]
    p = F.jsonop([stackes[162], stackes[149]], BinaryOp26, 1)
    stackes[163] = p[0]
    p = F.jsonop([stackes[163]], Convolution122, 1)
    stackes[164] = p[0]
    p = F.jsonop([stackes[164]], UnaryOp3, 1)
    stackes[165] = p[0]
    p = F.jsonop([stackes[165]], ConvolutionDepthwise166, 1)
    stackes[166] = p[0]
    p = F.jsonop([stackes[166]], UnaryOp3, 1)
    stackes[167] = p[0]
    p = F.jsonop([stackes[167]], ConvertTensor6, 1)
    stackes[168] = p[0]
    p = F.jsonop([stackes[168]], Reduction7, 1)
    stackes[169] = p[0]
    p = F.jsonop([stackes[169]], ConvertTensor1, 1)
    stackes[170] = p[0]
    p = F.jsonop([stackes[170]], Convolution129, 1)
    stackes[171] = p[0]
    p = F.jsonop([stackes[171]], UnaryOp3, 1)
    stackes[172] = p[0]
    p = F.jsonop([stackes[172]], Convolution131, 1)
    stackes[173] = p[0]
    p = F.jsonop([stackes[173]], UnaryOp12, 1)
    stackes[174] = p[0]
    p = F.jsonop([stackes[167], stackes[174]], BinaryOp13, 1)
    stackes[175] = p[0]
    p = F.jsonop([stackes[175]], Convolution176, 1)
    stackes[176] = p[0]
    p = F.jsonop([stackes[176]], Convolution177, 1)
    stackes[177] = p[0]
    p = F.jsonop([stackes[177]], UnaryOp3, 1)
    stackes[178] = p[0]
    p = F.jsonop([stackes[178]], ConvolutionDepthwise179, 1)
    stackes[179] = p[0]
    p = F.jsonop([stackes[179]], UnaryOp3, 1)
    stackes[180] = p[0]
    p = F.jsonop([stackes[180]], ConvertTensor6, 1)
    stackes[181] = p[0]
    p = F.jsonop([stackes[181]], Reduction7, 1)
    stackes[182] = p[0]
    p = F.jsonop([stackes[182]], ConvertTensor1, 1)
    stackes[183] = p[0]
    p = F.jsonop([stackes[183]], Convolution184, 1)
    stackes[184] = p[0]
    p = F.jsonop([stackes[184]], UnaryOp3, 1)
    stackes[185] = p[0]
    p = F.jsonop([stackes[185]], Convolution186, 1)
    stackes[186] = p[0]
    p = F.jsonop([stackes[186]], UnaryOp12, 1)
    stackes[187] = p[0]
    p = F.jsonop([stackes[180], stackes[187]], BinaryOp13, 1)
    stackes[188] = p[0]
    p = F.jsonop([stackes[188]], Convolution189, 1)
    stackes[189] = p[0]
    p = F.jsonop([stackes[189], stackes[176]], BinaryOp26, 1)
    stackes[190] = p[0]
    p = F.jsonop([stackes[190]], Convolution177, 1)
    stackes[191] = p[0]
    p = F.jsonop([stackes[191]], UnaryOp3, 1)
    stackes[192] = p[0]
    p = F.jsonop([stackes[192]], ConvolutionDepthwise179, 1)
    stackes[193] = p[0]
    p = F.jsonop([stackes[193]], UnaryOp3, 1)
    stackes[194] = p[0]
    p = F.jsonop([stackes[194]], ConvertTensor6, 1)
    stackes[195] = p[0]
    p = F.jsonop([stackes[195]], Reduction7, 1)
    stackes[196] = p[0]
    p = F.jsonop([stackes[196]], ConvertTensor1, 1)
    stackes[197] = p[0]
    p = F.jsonop([stackes[197]], Convolution184, 1)
    stackes[198] = p[0]
    p = F.jsonop([stackes[198]], UnaryOp3, 1)
    stackes[199] = p[0]
    p = F.jsonop([stackes[199]], Convolution186, 1)
    stackes[200] = p[0]
    p = F.jsonop([stackes[200]], UnaryOp12, 1)
    stackes[201] = p[0]
    p = F.jsonop([stackes[194], stackes[201]], BinaryOp13, 1)
    stackes[202] = p[0]
    p = F.jsonop([stackes[202]], Convolution189, 1)
    stackes[203] = p[0]
    p = F.jsonop([stackes[203], stackes[190]], BinaryOp26, 1)
    stackes[204] = p[0]
    p = F.jsonop([stackes[204]], Convolution177, 1)
    stackes[205] = p[0]
    p = F.jsonop([stackes[205]], UnaryOp3, 1)
    stackes[206] = p[0]
    p = F.jsonop([stackes[206]], ConvolutionDepthwise179, 1)
    stackes[207] = p[0]
    p = F.jsonop([stackes[207]], UnaryOp3, 1)
    stackes[208] = p[0]
    p = F.jsonop([stackes[208]], ConvertTensor6, 1)
    stackes[209] = p[0]
    p = F.jsonop([stackes[209]], Reduction7, 1)
    stackes[210] = p[0]
    p = F.jsonop([stackes[210]], ConvertTensor1, 1)
    stackes[211] = p[0]
    p = F.jsonop([stackes[211]], Convolution184, 1)
    stackes[212] = p[0]
    p = F.jsonop([stackes[212]], UnaryOp3, 1)
    stackes[213] = p[0]
    p = F.jsonop([stackes[213]], Convolution186, 1)
    stackes[214] = p[0]
    p = F.jsonop([stackes[214]], UnaryOp12, 1)
    stackes[215] = p[0]
    p = F.jsonop([stackes[208], stackes[215]], BinaryOp13, 1)
    stackes[216] = p[0]
    p = F.jsonop([stackes[216]], Convolution189, 1)
    stackes[217] = p[0]
    p = F.jsonop([stackes[217], stackes[204]], BinaryOp26, 1)
    stackes[218] = p[0]
    p = F.jsonop([stackes[218]], Convolution177, 1)
    stackes[219] = p[0]
    p = F.jsonop([stackes[219]], UnaryOp3, 1)
    stackes[220] = p[0]
    p = F.jsonop([stackes[220]], ConvolutionDepthwise221, 1)
    stackes[221] = p[0]
    p = F.jsonop([stackes[221]], UnaryOp3, 1)
    stackes[222] = p[0]
    p = F.jsonop([stackes[222]], ConvertTensor6, 1)
    stackes[223] = p[0]
    p = F.jsonop([stackes[223]], Reduction7, 1)
    stackes[224] = p[0]
    p = F.jsonop([stackes[224]], ConvertTensor1, 1)
    stackes[225] = p[0]
    p = F.jsonop([stackes[225]], Convolution184, 1)
    stackes[226] = p[0]
    p = F.jsonop([stackes[226]], UnaryOp3, 1)
    stackes[227] = p[0]
    p = F.jsonop([stackes[227]], Convolution186, 1)
    stackes[228] = p[0]
    p = F.jsonop([stackes[228]], UnaryOp12, 1)
    stackes[229] = p[0]
    p = F.jsonop([stackes[222], stackes[229]], BinaryOp13, 1)
    stackes[230] = p[0]
    p = F.jsonop([stackes[230]], Convolution231, 1)
    stackes[231] = p[0]
    p = F.jsonop([stackes[231]], Convolution232, 1)
    stackes[232] = p[0]
    p = F.jsonop([stackes[232]], UnaryOp3, 1)
    stackes[233] = p[0]
    p = F.jsonop([stackes[233]], ConvolutionDepthwise234, 1)
    stackes[234] = p[0]
    p = F.jsonop([stackes[234]], UnaryOp3, 1)
    stackes[235] = p[0]
    p = F.jsonop([stackes[235]], ConvertTensor6, 1)
    stackes[236] = p[0]
    p = F.jsonop([stackes[236]], Reduction7, 1)
    stackes[237] = p[0]
    p = F.jsonop([stackes[237]], ConvertTensor1, 1)
    stackes[238] = p[0]
    p = F.jsonop([stackes[238]], Convolution239, 1)
    stackes[239] = p[0]
    p = F.jsonop([stackes[239]], UnaryOp3, 1)
    stackes[240] = p[0]
    p = F.jsonop([stackes[240]], Convolution241, 1)
    stackes[241] = p[0]
    p = F.jsonop([stackes[241]], UnaryOp12, 1)
    stackes[242] = p[0]
    p = F.jsonop([stackes[235], stackes[242]], BinaryOp13, 1)
    stackes[243] = p[0]
    p = F.jsonop([stackes[243]], Convolution244, 1)
    stackes[244] = p[0]
    p = F.jsonop([stackes[244], stackes[231]], BinaryOp26, 1)
    stackes[245] = p[0]
    p = F.jsonop([stackes[245]], Convolution232, 1)
    stackes[246] = p[0]
    p = F.jsonop([stackes[246]], UnaryOp3, 1)
    stackes[247] = p[0]
    p = F.jsonop([stackes[247]], ConvolutionDepthwise234, 1)
    stackes[248] = p[0]
    p = F.jsonop([stackes[248]], UnaryOp3, 1)
    stackes[249] = p[0]
    p = F.jsonop([stackes[249]], ConvertTensor6, 1)
    stackes[250] = p[0]
    p = F.jsonop([stackes[250]], Reduction7, 1)
    stackes[251] = p[0]
    p = F.jsonop([stackes[251]], ConvertTensor1, 1)
    stackes[252] = p[0]
    p = F.jsonop([stackes[252]], Convolution239, 1)
    stackes[253] = p[0]
    p = F.jsonop([stackes[253]], UnaryOp3, 1)
    stackes[254] = p[0]
    p = F.jsonop([stackes[254]], Convolution241, 1)
    stackes[255] = p[0]
    p = F.jsonop([stackes[255]], UnaryOp12, 1)
    stackes[256] = p[0]
    p = F.jsonop([stackes[249], stackes[256]], BinaryOp13, 1)
    stackes[257] = p[0]
    p = F.jsonop([stackes[257]], Convolution244, 1)
    stackes[258] = p[0]
    p = F.jsonop([stackes[258], stackes[245]], BinaryOp26, 1)
    stackes[259] = p[0]
    p = F.jsonop([stackes[259]], Convolution232, 1)
    stackes[260] = p[0]
    p = F.jsonop([stackes[260]], UnaryOp3, 1)
    stackes[261] = p[0]
    p = F.jsonop([stackes[261]], ConvolutionDepthwise234, 1)
    stackes[262] = p[0]
    p = F.jsonop([stackes[262]], UnaryOp3, 1)
    stackes[263] = p[0]
    p = F.jsonop([stackes[263]], ConvertTensor6, 1)
    stackes[264] = p[0]
    p = F.jsonop([stackes[264]], Reduction7, 1)
    stackes[265] = p[0]
    p = F.jsonop([stackes[265]], ConvertTensor1, 1)
    stackes[266] = p[0]
    p = F.jsonop([stackes[266]], Convolution239, 1)
    stackes[267] = p[0]
    p = F.jsonop([stackes[267]], UnaryOp3, 1)
    stackes[268] = p[0]
    p = F.jsonop([stackes[268]], Convolution241, 1)
    stackes[269] = p[0]
    p = F.jsonop([stackes[269]], UnaryOp12, 1)
    stackes[270] = p[0]
    p = F.jsonop([stackes[263], stackes[270]], BinaryOp13, 1)
    stackes[271] = p[0]
    p = F.jsonop([stackes[271]], Convolution244, 1)
    stackes[272] = p[0]
    p = F.jsonop([stackes[272], stackes[259]], BinaryOp26, 1)
    stackes[273] = p[0]
    p = F.jsonop([stackes[273]], Convolution232, 1)
    stackes[274] = p[0]
    p = F.jsonop([stackes[274]], UnaryOp3, 1)
    stackes[275] = p[0]
    p = F.jsonop([stackes[275]], ConvolutionDepthwise234, 1)
    stackes[276] = p[0]
    p = F.jsonop([stackes[276]], UnaryOp3, 1)
    stackes[277] = p[0]
    p = F.jsonop([stackes[277]], ConvertTensor6, 1)
    stackes[278] = p[0]
    p = F.jsonop([stackes[278]], Reduction7, 1)
    stackes[279] = p[0]
    p = F.jsonop([stackes[279]], ConvertTensor1, 1)
    stackes[280] = p[0]
    p = F.jsonop([stackes[280]], Convolution239, 1)
    stackes[281] = p[0]
    p = F.jsonop([stackes[281]], UnaryOp3, 1)
    stackes[282] = p[0]
    p = F.jsonop([stackes[282]], Convolution241, 1)
    stackes[283] = p[0]
    p = F.jsonop([stackes[283]], UnaryOp12, 1)
    stackes[284] = p[0]
    p = F.jsonop([stackes[277], stackes[284]], BinaryOp13, 1)
    stackes[285] = p[0]
    p = F.jsonop([stackes[285]], Convolution244, 1)
    stackes[286] = p[0]
    p = F.jsonop([stackes[286], stackes[273]], BinaryOp26, 1)
    stackes[287] = p[0]
    p = F.jsonop([stackes[287]], Convolution232, 1)
    stackes[288] = p[0]
    p = F.jsonop([stackes[288]], UnaryOp3, 1)
    stackes[289] = p[0]
    p = F.jsonop([stackes[289]], ConvolutionDepthwise290, 1)
    stackes[290] = p[0]
    p = F.jsonop([stackes[290]], UnaryOp3, 1)
    stackes[291] = p[0]
    p = F.jsonop([stackes[291]], ConvertTensor6, 1)
    stackes[292] = p[0]
    p = F.jsonop([stackes[292]], Reduction7, 1)
    stackes[293] = p[0]
    p = F.jsonop([stackes[293]], ConvertTensor1, 1)
    stackes[294] = p[0]
    p = F.jsonop([stackes[294]], Convolution239, 1)
    stackes[295] = p[0]
    p = F.jsonop([stackes[295]], UnaryOp3, 1)
    stackes[296] = p[0]
    p = F.jsonop([stackes[296]], Convolution241, 1)
    stackes[297] = p[0]
    p = F.jsonop([stackes[297]], UnaryOp12, 1)
    stackes[298] = p[0]
    p = F.jsonop([stackes[291], stackes[298]], BinaryOp13, 1)
    stackes[299] = p[0]
    p = F.jsonop([stackes[299]], Convolution300, 1)
    stackes[300] = p[0]
    p = F.jsonop([stackes[300]], Convolution301, 1)
    stackes[301] = p[0]
    p = F.jsonop([stackes[301]], UnaryOp3, 1)
    stackes[302] = p[0]
    p = F.jsonop([stackes[302]], ConvolutionDepthwise303, 1)
    stackes[303] = p[0]
    p = F.jsonop([stackes[303]], UnaryOp3, 1)
    stackes[304] = p[0]
    p = F.jsonop([stackes[304]], ConvertTensor6, 1)
    stackes[305] = p[0]
    p = F.jsonop([stackes[305]], Reduction7, 1)
    stackes[306] = p[0]
    p = F.jsonop([stackes[306]], ConvertTensor1, 1)
    stackes[307] = p[0]
    p = F.jsonop([stackes[307]], Convolution308, 1)
    stackes[308] = p[0]
    p = F.jsonop([stackes[308]], UnaryOp3, 1)
    stackes[309] = p[0]
    p = F.jsonop([stackes[309]], Convolution310, 1)
    stackes[310] = p[0]
    p = F.jsonop([stackes[310]], UnaryOp12, 1)
    stackes[311] = p[0]
    p = F.jsonop([stackes[304], stackes[311]], BinaryOp13, 1)
    stackes[312] = p[0]
    p = F.jsonop([stackes[312]], Convolution313, 1)
    stackes[313] = p[0]
    p = F.jsonop([stackes[313], stackes[300]], BinaryOp26, 1)
    stackes[314] = p[0]
    p = F.jsonop([stackes[314]], Convolution315, 1)
    stackes[315] = p[0]
    p = F.jsonop([stackes[315]], UnaryOp3, 1)
    stackes[316] = p[0]
    p = F.jsonop([stackes[316]], Pooling3D317, 1)
    stackes[317] = p[0]
    p = F.jsonop([stackes[317]], Flatten318, 1)
    stackes[318] = p[0]
    p = F.jsonop([], Const319, 1)
    stackes[319] = p[0]
    p = F.jsonop([stackes[318], stackes[319]], Reshape320, 1)
    stackes[320] = p[0]
    p = F.jsonop([stackes[320]], Convolution321, 1)
    stackes[321] = p[0]
    p = F.jsonop([stackes[321]], ConvertTensor6, 1)
    stackes[322] = p[0]
    p = F.jsonop([stackes[318]], Shape323, 1)
    stackes[323] = p[0]
    p = F.jsonop([], Const324, 1)
    stackes[324] = p[0]
    p = F.jsonop([stackes[318]], Rank325, 1)
    stackes[325] = p[0]
    p = F.jsonop([], Const326, 1)
    stackes[326] = p[0]
    p = F.jsonop([stackes[325], stackes[326]], BinaryOp327, 1)
    stackes[327] = p[0]
    p = F.jsonop([stackes[327]], Unsqueeze328, 1)
    stackes[328] = p[0]
    p = F.jsonop([stackes[323], stackes[324], stackes[328]], SliceTf329, 1)
    stackes[329] = p[0]
    p = F.jsonop([], Const330, 1)
    stackes[330] = p[0]
    p = F.jsonop([stackes[323], stackes[328], stackes[330]], SliceTf329, 1)
    stackes[331] = p[0]
    p = F.jsonop([], Const332, 1)
    stackes[332] = p[0]
    p = F.jsonop([stackes[329], stackes[331], stackes[332]], Concat333, 1)
    stackes[333] = p[0]
    p = F.jsonop([stackes[322], stackes[333]], Reshape320, 1)
    stackes[334] = p[0]
    stackes[334].name = 'logits'
    return stackes[334]
