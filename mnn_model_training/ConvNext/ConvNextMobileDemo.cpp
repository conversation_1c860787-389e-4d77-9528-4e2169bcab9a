//
//  ConvNextMobileDemo.cpp
//  MNN
//
//  ConvNext Mobile Training Demo using MNN Training Framework
//

#include <MNN/expr/Executor.hpp>
#include <MNN/expr/Optimizer.hpp>
#include <MNN/Interpreter.hpp>
#include <cmath>
#include <iostream>
#include <vector>
#include <fstream>
#include <chrono>
#include <string>
#include <iomanip>
#include <numeric>
#include <sstream>
#include "DemoUnit.hpp"
#include "ImageDataset.hpp"
#include "DataLoader.hpp"
#include "ADAM.hpp"
#include "SGD.hpp"
#include "LearningRateScheduler.hpp"
#include "Loss.hpp"
#include "RandomGenerator.hpp"

using namespace MNN;
using namespace MNN::Express;
using namespace MNN::Train;
using namespace std;

// 时间测量辅助结构
struct TimingStats {
    vector<double> iterationTimes;        // 总iteration时间
    vector<double> modelTrainingTimes;    // 纯模型训练时间
    vector<double> dataPreparationTimes; // 数据准备时间
    vector<double> epochTimes;
    double totalTrainingTime = 0.0;

    // 日志记录
    ofstream logFile;
    int currentBatchSize = 0;
    int currentEpoch = 0;

    // 初始化日志文件（与终端输出格式一致）
    void initializeLog(int batchSize, int numClasses, float initialLR, int epochs) {
        currentBatchSize = batchSize;

        // 生成带时间戳的日志文件名
        auto now = chrono::system_clock::now();
        auto time_t = chrono::system_clock::to_time_t(now);
        auto tm = *localtime(&time_t);

        stringstream ss;
        ss << "convnext_training_batch" << batchSize << "_"
           << put_time(&tm, "%Y%m%d_%H%M%S") << ".log";

        logFile.open(ss.str());
        if (logFile.is_open()) {
            // 记录与终端输出完全一致的格式
            logFile << string(60, '=') << endl;
            logFile << "🚀 Testing Batch Size: " << batchSize << endl;
            logFile << string(60, '=') << endl;
            logFile << endl;
            logFile << "Training parameters:" << endl;
            logFile << "  Batch size: " << batchSize << endl;
            logFile << "  Number of classes: " << numClasses << endl;
            logFile << "  Initial learning rate: " << initialLR << endl;
            logFile << "  Epochs: " << epochs << endl;
            logFile.flush();
        }
    }

    void addIterationTime(double totalTime, double modelTime, double dataTime) {
        iterationTimes.push_back(totalTime);
        modelTrainingTimes.push_back(modelTime);
        dataPreparationTimes.push_back(dataTime);
    }

    // 记录iteration详细信息到日志（与终端输出格式完全一致）
    void logIteration(int iteration, int totalIterations, float loss,
                     double modelTime, double dataTime, double totalTime, float lr) {
        if (logFile.is_open()) {
            logFile << "  Iteration " << iteration << "/" << totalIterations
                    << ", Loss: " << fixed << setprecision(3) << loss
                    << ", Time(Model Training): " << fixed << setprecision(3) << modelTime << "s"
                    << ", Time(Data Preparation): " << fixed << setprecision(3) << dataTime << "s"
                    << ", Time(Total Iteration): " << fixed << setprecision(3) << totalTime << "s"
                    << ", LR: " << scientific << setprecision(2) << lr << endl;
            logFile.flush();
        }
    }

    void addEpochTime(double time) {
        epochTimes.push_back(time);
    }

    // 设置当前epoch（与终端输出格式一致）
    void setCurrentEpoch(int epoch, int totalEpochs) {
        currentEpoch = epoch;
        if (logFile.is_open()) {
            logFile << "\n--- Epoch " << epoch << "/" << totalEpochs << " ---" << endl;
            logFile.flush();
        }
    }

    // 记录epoch完成信息（与终端输出格式一致）
    void logEpochComplete(int epoch, double epochTime, float avgLoss,
                         double avgIterationTime, double minIterationTime, double maxIterationTime) {
        if (logFile.is_open()) {
            logFile << fixed << setprecision(3);
            logFile << "Epoch " << epoch << " completed in " << epochTime
                    << " seconds. Average loss: " << avgLoss << endl;
            logFile << "  Iteration timing - Avg: " << avgIterationTime
                    << "s, Min: " << minIterationTime
                    << "s, Max: " << maxIterationTime << "s" << endl;
            logFile.flush();
        }
    }

    // 记录训练完成的汇总信息（与终端输出格式一致）
    void logTrainingComplete() {
        if (logFile.is_open()) {
            logFile << "\n=== Training Completed ===" << endl;
            logFile << fixed << setprecision(3);
            logFile << "Total training time: " << totalTrainingTime << " seconds ("
                    << (totalTrainingTime / 60.0) << " minutes)" << endl;

            // 记录详细的时间统计（与printDetailedStats格式一致）
            logFile << "\n=== Detailed Timing Statistics ===" << endl;

            if (!iterationTimes.empty()) {
                logFile << "📊 Iteration Timing Breakdown:" << endl;
                logFile << "  Total iterations: " << iterationTimes.size() << endl;
                logFile << "\n  🔥 Pure Model Training (Forward + Backward + Update):" << endl;
                logFile << "    Average: " << getAverageModelTrainingTime() << " seconds" << endl;
                logFile << "    Min: " << getMinModelTrainingTime() << " seconds" << endl;
                logFile << "    Max: " << getMaxModelTrainingTime() << " seconds" << endl;

                logFile << "\n  📦 Data Preparation (Load + Copy + Preprocess):" << endl;
                logFile << "    Average: " << getAverageDataPreparationTime() << " seconds" << endl;

                logFile << "\n  ⏱️  Total Iteration Time:" << endl;
                logFile << "    Average: " << getAverageIterationTime() << " seconds" << endl;
                logFile << "    Min: " << getMinIterationTime() << " seconds" << endl;
                logFile << "    Max: " << getMaxIterationTime() << " seconds" << endl;

                logFile << "\n  🚀 Performance Metrics:" << endl;
                logFile << "    Model training efficiency: " << fixed << setprecision(1)
                        << (getAverageModelTrainingTime() / getAverageIterationTime() * 100) << "%" << endl;
                logFile << "    Data preparation overhead: " << fixed << setprecision(1)
                        << (getAverageDataPreparationTime() / getAverageIterationTime() * 100) << "%" << endl;
                logFile << "    Estimated pure training time per 1000 iterations: "
                        << (getAverageModelTrainingTime() * 1000 / 60.0) << " minutes" << endl;
            }

            logFile << "\n⏰ Total Training Time: " << totalTrainingTime << " seconds ("
                    << (totalTrainingTime / 60.0) << " minutes)" << endl;

            logFile << "\n✅ Batch size " << currentBatchSize << " testing completed!" << endl;
            logFile.close();
        }
    }

    double getAverageIterationTime() const {
        if (iterationTimes.empty()) return 0.0;
        return accumulate(iterationTimes.begin(), iterationTimes.end(), 0.0) / iterationTimes.size();
    }

    double getAverageModelTrainingTime() const {
        if (modelTrainingTimes.empty()) return 0.0;
        return accumulate(modelTrainingTimes.begin(), modelTrainingTimes.end(), 0.0) / modelTrainingTimes.size();
    }

    double getAverageDataPreparationTime() const {
        if (dataPreparationTimes.empty()) return 0.0;
        return accumulate(dataPreparationTimes.begin(), dataPreparationTimes.end(), 0.0) / dataPreparationTimes.size();
    }

    double getMinIterationTime() const {
        if (iterationTimes.empty()) return 0.0;
        return *min_element(iterationTimes.begin(), iterationTimes.end());
    }

    double getMaxIterationTime() const {
        if (iterationTimes.empty()) return 0.0;
        return *max_element(iterationTimes.begin(), iterationTimes.end());
    }

    double getMinModelTrainingTime() const {
        if (modelTrainingTimes.empty()) return 0.0;
        return *min_element(modelTrainingTimes.begin(), modelTrainingTimes.end());
    }

    double getMaxModelTrainingTime() const {
        if (modelTrainingTimes.empty()) return 0.0;
        return *max_element(modelTrainingTimes.begin(), modelTrainingTimes.end());
    }

    void printDetailedStats() const {
        cout << "\n=== Detailed Timing Statistics ===" << endl;
        cout << fixed << setprecision(3);

        if (!iterationTimes.empty()) {
            cout << "📊 Iteration Timing Breakdown:" << endl;
            cout << "  Total iterations: " << iterationTimes.size() << endl;
            cout << "\n  🔥 Pure Model Training (Forward + Backward + Update):" << endl;
            cout << "    Average: " << getAverageModelTrainingTime() << " seconds" << endl;
            cout << "    Min: " << getMinModelTrainingTime() << " seconds" << endl;
            cout << "    Max: " << getMaxModelTrainingTime() << " seconds" << endl;

            cout << "\n  📦 Data Preparation (Load + Copy + Preprocess):" << endl;
            cout << "    Average: " << getAverageDataPreparationTime() << " seconds" << endl;

            cout << "\n  ⏱️  Total Iteration Time:" << endl;
            cout << "    Average: " << getAverageIterationTime() << " seconds" << endl;
            cout << "    Min: " << getMinIterationTime() << " seconds" << endl;
            cout << "    Max: " << getMaxIterationTime() << " seconds" << endl;

            cout << "\n  🚀 Performance Metrics:" << endl;
            cout << "    Model training efficiency: " << fixed << setprecision(1)
                 << (getAverageModelTrainingTime() / getAverageIterationTime() * 100) << "%" << endl;
            cout << "    Data preparation overhead: " << fixed << setprecision(1)
                 << (getAverageDataPreparationTime() / getAverageIterationTime() * 100) << "%" << endl;
            cout << "    Estimated pure training time per 1000 iterations: "
                 << (getAverageModelTrainingTime() * 1000 / 60.0) << " minutes" << endl;
        }

        if (!epochTimes.empty()) {
            cout << "\n📈 Epoch Timing:" << endl;
            cout << "  Total epochs: " << epochTimes.size() << endl;
            for (size_t i = 0; i < epochTimes.size(); ++i) {
                cout << "  Epoch " << (i + 1) << ": " << epochTimes[i] << " seconds" << endl;
            }
            double avgEpochTime = accumulate(epochTimes.begin(), epochTimes.end(), 0.0) / epochTimes.size();
            cout << "  Average time per epoch: " << avgEpochTime << " seconds" << endl;
        }

        cout << "\n⏰ Total Training Time: " << totalTrainingTime << " seconds ("
             << (totalTrainingTime / 60.0) << " minutes)" << endl;
    }
};

// 打印不同batch size的性能对比
void printBatchSizeComparison(const vector<pair<int, TimingStats>>& results) {
    cout << "\n" << string(80, '=') << endl;
    cout << "🏆 BATCH SIZE PERFORMANCE COMPARISON" << endl;
    cout << string(80, '=') << endl;

    cout << fixed << setprecision(3);
    cout << left << setw(12) << "Batch Size"
         << setw(18) << "Pure Model (s)"
         << setw(15) << "Data Prep (s)"
         << setw(15) << "Total Iter (s)"
         << setw(15) << "Efficiency (%)"
         << setw(20) << "Throughput (samples/s)" << endl;
    cout << string(80, '-') << endl;

    for (const auto& result : results) {
        int batchSize = result.first;
        const TimingStats& stats = result.second;

        double avgModelTime = stats.getAverageModelTrainingTime();
        double avgDataTime = stats.getAverageDataPreparationTime();
        double avgIterTime = stats.getAverageIterationTime();
        double efficiency = (avgModelTime / avgIterTime) * 100;  // model training efficiency
        double throughput = batchSize / avgModelTime;  // samples per second based on pure model time

        cout << left << setw(12) << batchSize
             << setw(18) << avgModelTime
             << setw(15) << avgDataTime
             << setw(15) << avgIterTime
             << setw(15) << efficiency
             << setw(20) << throughput << endl;
    }

    cout << string(80, '-') << endl;

    // 找出最佳性能
    if (!results.empty()) {
        auto bestThroughput = max_element(results.begin(), results.end(),
            [](const pair<int, TimingStats>& a, const pair<int, TimingStats>& b) {
                double throughputA = a.first / a.second.getAverageModelTrainingTime();
                double throughputB = b.first / b.second.getAverageModelTrainingTime();
                return throughputA < throughputB;
            });

        cout << "🥇 Best throughput (pure model): Batch size " << bestThroughput->first
             << " (" << fixed << setprecision(2)
             << (bestThroughput->first / bestThroughput->second.getAverageModelTrainingTime())
             << " samples/s)" << endl;

        auto fastestModel = min_element(results.begin(), results.end(),
            [](const pair<int, TimingStats>& a, const pair<int, TimingStats>& b) {
                return a.second.getAverageModelTrainingTime() < b.second.getAverageModelTrainingTime();
            });

        cout << "⚡ Fastest pure model training: Batch size " << fastestModel->first
             << " (" << fixed << setprecision(3)
             << fastestModel->second.getAverageModelTrainingTime() << "s per iteration)" << endl;

        auto mostEfficient = max_element(results.begin(), results.end(),
            [](const pair<int, TimingStats>& a, const pair<int, TimingStats>& b) {
                double efficiencyA = a.second.getAverageModelTrainingTime() / a.second.getAverageIterationTime();
                double efficiencyB = b.second.getAverageModelTrainingTime() / b.second.getAverageIterationTime();
                return efficiencyA < efficiencyB;
            });

        cout << "🎯 Most efficient (least overhead): Batch size " << mostEfficient->first
             << " (" << fixed << setprecision(1)
             << (mostEfficient->second.getAverageModelTrainingTime() / mostEfficient->second.getAverageIterationTime() * 100)
             << "% efficiency)" << endl;
    }

    cout << "\nBatch size testing completed! 🎉" << endl;
}

class ConvNextMobileTraining : public DemoUnit {
public:
    virtual int run(int argc, const char* argv[]) override {
        if (argc < 3) {
            std::cout << "Usage: ./runTrainDemo.out ConvNextMobileTraining <train_images_folder> <train_images_txt> [test_images_folder] [test_images_txt]" << std::endl;
            std::cout << "Example: ./runTrainDemo.out ConvNextMobileTraining ./data/train/ ./data/train.txt" << std::endl;
            return 0;
        }
        
        std::string trainImagesFolder = argv[1];
        std::string trainImagesTxt = argv[2];
        std::string testImagesFolder = "";
        std::string testImagesTxt = "";
        
        bool hasTestData = false;
        if (argc >= 5) {
            testImagesFolder = argv[3];
            testImagesTxt = argv[4];
            hasTestData = true;
        }
        
        std::cout << "=== ConvNext Mobile Training Demo ===" << std::endl;
        std::cout << "Training data: " << trainImagesFolder << " -> " << trainImagesTxt << std::endl;
        if (hasTestData) {
            std::cout << "Test data: " << testImagesFolder << " -> " << testImagesTxt << std::endl;
        }
        
        // 设置随机种子
        RandomGenerator::generator(42);
        
        // 配置执行器 - 参考MobileNet的方式
        MNNForwardType backend = MNN_FORWARD_OPENCL; // 使用GPU
        int threadNumber = 1;

        auto exe = Executor::getGlobalExecutor();
        BackendConfig config;
        // 设置FP32精度
        config.precision = BackendConfig::Precision_High;
        exe->setGlobalExecutorConfig(backend, config, threadNumber);

        cout << "Using GPU (OpenCL) backend for ConvNext training with FP32 precision" << endl;

        // 训练参数
        const vector<int> batchSizes = {1, 4, 8, 16, 32, 64};
        const int numClasses = 1000;  // ImageNet类别数
        const float initialLR = 0.0001f;  // 较小的学习率适合手机端
        const int epochs = 2;  // 每个batch size测试1个epoch
        const int trainNumWorkers = 0;  // 手机端不使用多线程数据加载

        // 存储所有batch size的性能结果
        vector<pair<int, TimingStats>> allResults;

        // 测试不同的batch size
        for (int batchSize : batchSizes) {
            std::cout << "\n" << string(60, '=') << std::endl;
            std::cout << "🚀 Testing Batch Size: " << batchSize << std::endl;
            std::cout << string(60, '=') << std::endl;

            std::cout << "Training parameters:" << std::endl;
            std::cout << "  Batch size: " << batchSize << std::endl;
            std::cout << "  Number of classes: " << numClasses << std::endl;
            std::cout << "  Initial learning rate: " << initialLR << std::endl;
            std::cout << "  Epochs: " << epochs << std::endl;
        
        // 创建数据集配置 - ConvNext使用384x384输入
            auto converImagesToFormat = CV::RGB;
            int resizeHeight = 384;
            int resizeWidth = 384;
            
            // ImageNet标准化参数
            std::vector<float> means = {0.485f * 255.0f, 0.456f * 255.0f, 0.406f * 255.0f};
            std::vector<float> scales = {1.0f / (0.229f * 255.0f), 1.0f / (0.224f * 255.0f), 1.0f / (0.225f * 255.0f)};
            std::vector<float> cropFraction = {1.0f, 1.0f};
            bool centerOrRandomCrop = false;
            
            std::shared_ptr<ImageDataset::ImageConfig> datasetConfig(
                ImageDataset::ImageConfig::create(converImagesToFormat, resizeHeight, resizeWidth, 
                                                scales, means, cropFraction, centerOrRandomCrop)
            );
            
            // 创建训练数据集
            std::cout << "\nCreating training dataset..." << std::endl;
            std::cout << "Dataset folder: " << trainImagesFolder << std::endl;
            std::cout << "Dataset file: " << trainImagesTxt << std::endl;

            bool readAllImagesToMemory = false;
            auto trainDataset = ImageDataset::create(trainImagesFolder, trainImagesTxt, datasetConfig.get(), readAllImagesToMemory);

            if (!trainDataset.mDataset) {
                MNN_ERROR("Failed to create training dataset\n");
                return -1;
            }
            std::cout << "Training dataset created successfully" << std::endl;
            
            // 创建数据加载器
            auto trainDataLoader = trainDataset.createLoader(batchSize, true, true, trainNumWorkers);
            const int trainIterations = trainDataLoader->iterNumber();
            std::cout << "Training iterations per epoch: " << trainIterations << std::endl;
            
            // 加载训练模型
            std::cout << "\nLoading ConvNext training model with MSE loss..." << std::endl;
            std::cout << "Model file size: ";
            std::ifstream file("convnext_train_mse.mnn", std::ios::binary | std::ios::ate);
            if (file.is_open()) {
                std::streamsize size = file.tellg();
                std::cout << (size / 1024 / 1024) << " MB" << std::endl;
                file.close();
            }

            std::shared_ptr<MNN::Interpreter> net(MNN::Interpreter::createFromFile("convnext_train_mse.mnn"));
            if (net == nullptr) {
                MNN_ERROR("Failed to load training model: convnext_train_mse.mnn\n");
                return -1;
            }
            std::cout << "Model loaded successfully!" << std::endl;
            
            // 创建Session
            MNN::ScheduleConfig scheduleConfig;
            scheduleConfig.type = MNN_FORWARD_OPENCL;
            scheduleConfig.numThread = 4;
            auto session = net->createSession(scheduleConfig);
            if (session == nullptr) {
                MNN_ERROR("Failed to create session\n");
                return -1;
            }
            
            // 获取输入输出张量
            auto inputTensor = net->getSessionInput(session, "pixel_values");
            auto labelTensor = net->getSessionInput(session, "logits_Compare");
            auto learningRateTensor = net->getSessionInput(session, "LearningRate");
            auto lossTensor = net->getSessionOutput(session, "Loss");
            
            if (!inputTensor || !labelTensor || !learningRateTensor || !lossTensor) {
                MNN_ERROR("Failed to get model tensors\n");
                return -1;
            }
            
            // 调整张量大小
            net->resizeTensor(inputTensor, {batchSize, 3, 384, 384});
            net->resizeTensor(labelTensor, {batchSize, numClasses});
            net->resizeTensor(learningRateTensor, {});
            net->resizeSession(session);
            
            std::cout << "Model loaded successfully!" << std::endl;
            
            // 开始训练
            std::cout << "\n=== Starting Training ===" << std::endl;
            auto startTime = std::chrono::high_resolution_clock::now();

            // 创建时间统计对象并初始化日志
            TimingStats timingStats;
            timingStats.initializeLog(batchSize, numClasses, initialLR, epochs);

            float currentLR = initialLR;

            for (int epoch = 0; epoch < epochs; ++epoch) {
                std::cout << "\n--- Epoch " << (epoch + 1) << "/" << epochs << " ---" << std::endl;

                // 设置当前epoch用于日志记录
                timingStats.setCurrentEpoch(epoch + 1, epochs);

                trainDataLoader->reset();
                float epochLoss = 0.0f;
                auto epochStartTime = std::chrono::high_resolution_clock::now();

                // 每个epoch的iteration时间统计
                vector<double> currentEpochIterationTimes;
                vector<double> currentEpochModelTimes;
                
                for (int i = 0; i < trainIterations; ++i) {
                    // 开始总iteration计时
                    auto iterationStartTime = std::chrono::high_resolution_clock::now();

                    // === 数据准备阶段 ===
                    auto trainData = trainDataLoader->next();
                    auto example = trainData[0];

                    // 获取输入数据和标签
                    auto inputVar = example.first[0];
                    auto labelVar = example.second[0];

                    // 复制图像数据
                    auto inputPtr = inputVar->readMap<float>();
                    auto inputTensorPtr = inputTensor->host<float>();
                    memcpy(inputTensorPtr, inputPtr, batchSize * 3 * 384 * 384 * sizeof(float));

                    // 创建标签向量用于MSE损失
                    auto labelPtr = labelVar->readMap<int>();
                    auto labelTensorPtr = labelTensor->host<float>();
                    memset(labelTensorPtr, 0, batchSize * numClasses * sizeof(float));
                    for (int b = 0; b < batchSize; ++b) {
                        int classId = labelPtr[b];
                        if (classId >= 0 && classId < numClasses) {
                            labelTensorPtr[b * numClasses + classId] = 1.0f;
                        }
                    }

                    // 设置学习率
                    auto lrPtr = learningRateTensor->host<float>();
                    *lrPtr = currentLR;

                    // 数据准备完成，开始模型训练计时
                    auto modelTrainingStartTime = std::chrono::high_resolution_clock::now();

                    // === 纯模型训练阶段 (前向传播 + 反向传播 + 参数更新) ===
                    net->runSession(session);

                    // 模型训练完成
                    auto modelTrainingEndTime = std::chrono::high_resolution_clock::now();

                    // 获取损失值
                    auto lossPtr = lossTensor->host<float>();
                    float loss = *lossPtr;
                    epochLoss += loss;

                    // 结束总iteration计时
                    auto iterationEndTime = std::chrono::high_resolution_clock::now();

                    // 计算各阶段时间
                    auto totalIterationDuration = std::chrono::duration_cast<std::chrono::milliseconds>(
                        iterationEndTime - iterationStartTime).count() / 1000.0;
                    auto modelTrainingDuration = std::chrono::duration_cast<std::chrono::milliseconds>(
                        modelTrainingEndTime - modelTrainingStartTime).count() / 1000.0;
                    auto dataPreparationDuration = std::chrono::duration_cast<std::chrono::milliseconds>(
                        modelTrainingStartTime - iterationStartTime).count() / 1000.0;

                    // 记录分离的时间统计
                    timingStats.addIterationTime(totalIterationDuration, modelTrainingDuration, dataPreparationDuration);
                    currentEpochIterationTimes.push_back(totalIterationDuration);
                    currentEpochModelTimes.push_back(modelTrainingDuration);

                    // 记录到日志文件
                    timingStats.logIteration(i, trainIterations, loss,
                                           modelTrainingDuration, dataPreparationDuration,
                                           totalIterationDuration, currentLR);

                    // 显示每个iteration的详细信息
                    std::cout << "  Iteration " << i << "/" << trainIterations
                              << ", Loss: " << fixed << setprecision(3) << loss
                              << ", Time(Model Training): " << fixed << setprecision(3) << modelTrainingDuration << "s"
                              << ", Time(Data Preparation): " << fixed << setprecision(3) << dataPreparationDuration << "s"
                              << ", Time(Total Iteration): " << fixed << setprecision(3) << totalIterationDuration << "s"
                              << ", LR: " << scientific << setprecision(2) << currentLR << std::endl;
                }
                
                auto epochEndTime = std::chrono::high_resolution_clock::now();
                auto epochDuration = std::chrono::duration_cast<std::chrono::milliseconds>(
                    epochEndTime - epochStartTime).count() / 1000.0;

                // 记录epoch时间
                timingStats.addEpochTime(epochDuration);

                float avgEpochLoss = epochLoss / trainIterations;

                // 计算当前epoch的iteration统计
                double avgIterationTime = accumulate(currentEpochIterationTimes.begin(),
                                                   currentEpochIterationTimes.end(), 0.0) / currentEpochIterationTimes.size();
                double minIterationTime = *min_element(currentEpochIterationTimes.begin(), currentEpochIterationTimes.end());
                double maxIterationTime = *max_element(currentEpochIterationTimes.begin(), currentEpochIterationTimes.end());

                // 计算模型训练时间统计
                double avgModelTime = accumulate(currentEpochModelTimes.begin(),
                                               currentEpochModelTimes.end(), 0.0) / currentEpochModelTimes.size();
                double minModelTime = *min_element(currentEpochModelTimes.begin(), currentEpochModelTimes.end());
                double maxModelTime = *max_element(currentEpochModelTimes.begin(), currentEpochModelTimes.end());

                // 记录epoch完成信息到日志
                timingStats.logEpochComplete(epoch + 1, epochDuration, avgEpochLoss,
                                           avgIterationTime, minIterationTime, maxIterationTime);

                std::cout << fixed << setprecision(3);
                std::cout << "Epoch " << (epoch + 1) << " completed in " << epochDuration
                          << " seconds. Average loss: " << avgEpochLoss << std::endl;
                std::cout << "  Iteration timing - Avg: " << avgIterationTime
                          << "s, Min: " << minIterationTime
                          << "s, Max: " << maxIterationTime << "s" << std::endl;
                
                // 学习率衰减
                currentLR *= 0.9f;
                std::cout << "Learning rate updated to: " << currentLR << std::endl;
                
                // 保存中间模型
                net->updateSessionToModel(session);
                auto buffer = net->getModelBuffer();
                std::string epochModelPath = "convnext_epoch_" + std::to_string(epoch + 1) + ".mnn";
                std::ofstream output(epochModelPath, std::ios::binary);
                output.write((const char*)buffer.first, buffer.second);
                output.close();
                std::cout << "Model saved: " << epochModelPath << std::endl;
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto totalDuration = std::chrono::duration_cast<std::chrono::milliseconds>(
                endTime - startTime).count() / 1000.0;

            // 记录总训练时间
            timingStats.totalTrainingTime = totalDuration;

            std::cout << "\n=== Training Completed ===" << std::endl;
            std::cout << fixed << setprecision(3);
            std::cout << "Total training time: " << totalDuration << " seconds ("
                      << (totalDuration / 60.0) << " minutes)" << std::endl;

            // 显示详细的时间统计
            timingStats.printDetailedStats();

            // 记录训练完成信息到日志并关闭日志文件
            timingStats.logTrainingComplete();

            // 存储当前batch size的结果
            allResults.emplace_back(batchSize, std::move(timingStats));

            // 保存当前batch size的模型
            net->updateSessionToModel(session);
            auto buffer = net->getModelBuffer();
            string modelName = "convnext_mobile_batch" + to_string(batchSize) + ".mnn";
            std::ofstream finalOutput(modelName, std::ios::binary);
            finalOutput.write((const char*)buffer.first, buffer.second);
            finalOutput.close();
            std::cout << "Model saved: " << modelName << std::endl;

            // 清理当前session
            net->releaseSession(session);

            std::cout << "✅ Batch size " << batchSize << " testing completed!" << std::endl;
        }

        // 显示所有batch size的性能对比
        printBatchSizeComparison(allResults);
        
        return 0;
    }
};

DemoUnitSetRegister(ConvNextMobileTraining, "ConvNextMobileTraining");
