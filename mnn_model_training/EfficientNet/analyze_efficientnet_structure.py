#!/usr/bin/env python3
"""
分析EfficientNet-B1的完整结构，用于在MNN中重建训练模块
"""

import torch
import timm
from collections import OrderedDict

def analyze_efficientnet_b1():
    """分析EfficientNet-B1的完整结构"""

    print("=== EfficientNet-B1 Structure Analysis ===")

    # 加载EfficientNet-B1模型
    model_name = "efficientnet_b1.ft_in1k"
    print(f"Loading model: {model_name}")

    try:
        model = timm.create_model(model_name, pretrained=True)
        model.eval()
    except Exception as e:
        print(f"Error loading model: {e}")
        # 尝试其他可用的EfficientNet-B1模型
        print("Trying alternative models...")
        alternatives = ["efficientnet_b1", "tf_efficientnet_b1", "tf_efficientnet_b1_ns"]
        for alt in alternatives:
            try:
                print(f"Trying {alt}...")
                model = timm.create_model(alt, pretrained=True)
                model.eval()
                model_name = alt
                break
            except:
                continue
        else:
            print("No EfficientNet-B1 model available")
            return

    print(f"Model loaded successfully: {model_name}")
    print(f"Number of classes: {model.num_classes}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()) / 1e6:.1f}M")
    
    # 分析模型结构
    print("\n=== Model Architecture ===")
    
    # 打印模型的主要组件
    print("\nMain components:")
    for name, module in model.named_children():
        print(f"  {name}: {type(module).__name__}")
        if hasattr(module, '__len__'):
            try:
                print(f"    Length: {len(module)}")
            except:
                pass
    
    # 详细分析blocks结构
    if hasattr(model, 'blocks'):
        print(f"\n=== Blocks Structure (Total: {len(model.blocks)}) ===")
        for i, block in enumerate(model.blocks):
            print(f"\nBlock {i}: {type(block).__name__}")
            
            # 分析每个block的配置
            if hasattr(block, 'conv_pw') or hasattr(block, 'conv_dw'):
                # MBConv block
                print(f"  Type: MBConv")
                
                if hasattr(block, 'conv_pw'):
                    pw_in = block.conv_pw.in_channels if hasattr(block.conv_pw, 'in_channels') else 'N/A'
                    pw_out = block.conv_pw.out_channels if hasattr(block.conv_pw, 'out_channels') else 'N/A'
                    print(f"  Pointwise: {pw_in} -> {pw_out}")
                
                if hasattr(block, 'conv_dw'):
                    dw_in = block.conv_dw.in_channels if hasattr(block.conv_dw, 'in_channels') else 'N/A'
                    dw_out = block.conv_dw.out_channels if hasattr(block.conv_dw, 'out_channels') else 'N/A'
                    kernel = block.conv_dw.kernel_size if hasattr(block.conv_dw, 'kernel_size') else 'N/A'
                    stride = block.conv_dw.stride if hasattr(block.conv_dw, 'stride') else 'N/A'
                    print(f"  Depthwise: {dw_in} -> {dw_out}, kernel={kernel}, stride={stride}")
                
                if hasattr(block, 'conv_pwl'):
                    pwl_in = block.conv_pwl.in_channels if hasattr(block.conv_pwl, 'in_channels') else 'N/A'
                    pwl_out = block.conv_pwl.out_channels if hasattr(block.conv_pwl, 'out_channels') else 'N/A'
                    print(f"  Pointwise Linear: {pwl_in} -> {pwl_out}")
            
            # 分析子模块
            for sub_name, sub_module in block.named_children():
                if hasattr(sub_module, 'in_channels') and hasattr(sub_module, 'out_channels'):
                    in_ch = sub_module.in_channels
                    out_ch = sub_module.out_channels
                    if hasattr(sub_module, 'kernel_size'):
                        kernel = sub_module.kernel_size
                        stride = getattr(sub_module, 'stride', 1)
                        print(f"    {sub_name}: {in_ch}->{out_ch}, k={kernel}, s={stride}")
                    else:
                        print(f"    {sub_name}: {in_ch}->{out_ch}")
    
    # 分析stem和head
    print(f"\n=== Stem and Head ===")
    if hasattr(model, 'conv_stem'):
        stem = model.conv_stem
        print(f"Stem: {stem.in_channels}->{stem.out_channels}, k={stem.kernel_size}, s={stem.stride}")
    
    if hasattr(model, 'conv_head'):
        head = model.conv_head
        print(f"Head: {head.in_channels}->{head.out_channels}, k={head.kernel_size}")
    
    if hasattr(model, 'classifier'):
        classifier = model.classifier
        if hasattr(classifier, 'in_features'):
            print(f"Classifier: {classifier.in_features}->{classifier.out_features}")
    
    # 生成MNN训练模块的C++代码模板
    print(f"\n=== Generated MNN Training Module Template ===")
    generate_mnn_module_template(model)

    # 对比当前实现
    print(f"\n=== Comparison with Current Implementation ===")
    compare_with_current_implementation(model)

def generate_mnn_module_template(model):
    """生成MNN训练模块的C++代码模板"""
    
    print("```cpp")
    print("class EfficientNetB1TrainingModule : public Module {")
    print("public:")
    print("    EfficientNetB1TrainingModule() {")
    
    # Stem
    if hasattr(model, 'conv_stem'):
        stem = model.conv_stem
        print(f"        // Stem: {stem.in_channels}->{stem.out_channels}")
        print("        NN::ConvOption stemOption;")
        print(f"        stemOption.kernelSize = {{{stem.kernel_size[0]}, {stem.kernel_size[1]}}};")
        print(f"        stemOption.channel = {{{stem.in_channels}, {stem.out_channels}}};")
        print("        stemOption.padMode = Express::SAME;")
        print(f"        stemOption.stride = {{{stem.stride[0]}, {stem.stride[1]}}};")
        print("        mStem.reset(NN::Conv(stemOption, false));")
        print("        mStemBn.reset(NN::BatchNorm(" + str(stem.out_channels) + "));")
        print()
    
    # Blocks
    if hasattr(model, 'blocks'):
        print(f"        // Blocks ({len(model.blocks)} total)")
        for i, block in enumerate(model.blocks):
            print(f"        // Block {i}")
            
            # 简化的block实现 - 每个block用一个卷积代替
            # 这里我们需要分析每个block的输入输出通道
            input_channels = None
            output_channels = None
            kernel_size = 3
            stride = 1
            
            # 尝试从block中提取信息
            for name, module in block.named_modules():
                if hasattr(module, 'in_channels') and hasattr(module, 'out_channels'):
                    if input_channels is None:
                        input_channels = module.in_channels
                    output_channels = module.out_channels
                    if hasattr(module, 'kernel_size') and module.kernel_size != (1, 1):
                        kernel_size = module.kernel_size[0]
                    if hasattr(module, 'stride') and module.stride != (1, 1):
                        stride = module.stride[0]
            
            if input_channels and output_channels:
                print(f"        NN::ConvOption block{i}Option;")
                print(f"        block{i}Option.kernelSize = {{{kernel_size}, {kernel_size}}};")
                print(f"        block{i}Option.channel = {{{input_channels}, {output_channels}}};")
                print("        block" + str(i) + "Option.padMode = Express::SAME;")
                print(f"        block{i}Option.stride = {{{stride}, {stride}}};")
                print(f"        mBlock{i}.reset(NN::Conv(block{i}Option, false));")
                print(f"        mBlock{i}Bn.reset(NN::BatchNorm({output_channels}));")
                print()
    
    # Head
    if hasattr(model, 'conv_head'):
        head = model.conv_head
        print(f"        // Head: {head.in_channels}->{head.out_channels}")
        print("        NN::ConvOption headOption;")
        print(f"        headOption.kernelSize = {{{head.kernel_size[0]}, {head.kernel_size[1]}}};")
        print(f"        headOption.channel = {{{head.in_channels}, {head.out_channels}}};")
        print("        headOption.padMode = Express::SAME;")
        print("        headOption.stride = {1, 1};")
        print("        mHead.reset(NN::Conv(headOption, false));")
        print("        mHeadBn.reset(NN::BatchNorm(" + str(head.out_channels) + "));")
        print()
    
    # Classifier
    if hasattr(model, 'classifier') and hasattr(model.classifier, 'in_features'):
        classifier = model.classifier
        print(f"        // Classifier: {classifier.in_features}->NUM_CLASSES")
        print(f"        mClassifier.reset(NN::Linear({classifier.in_features}, NUM_CLASSES));")
        print()
    
    print("        // Register all parameters")
    print("        registerModel({")
    print("            mStem, mStemBn,")
    
    if hasattr(model, 'blocks'):
        for i in range(len(model.blocks)):
            print(f"            mBlock{i}, mBlock{i}Bn,")
    
    print("            mHead, mHeadBn,")
    print("            mClassifier")
    print("        });")
    print("    }")
    print("};")
    print("```")

def compare_with_current_implementation(model):
    """对比当前MNN实现与真实EfficientNet结构"""

    print("Current MNN Implementation vs Real EfficientNet-B1:")
    print("=" * 60)

    # 当前实现的结构
    current_blocks = [
        {"name": "Stem", "in_ch": 3, "out_ch": 32, "kernel": 3, "stride": 2},
        {"name": "Block0", "in_ch": 32, "out_ch": 16, "kernel": 3, "stride": 1},
        {"name": "Block1", "in_ch": 16, "out_ch": 16, "kernel": 3, "stride": 1},
        {"name": "Block2", "in_ch": 16, "out_ch": 24, "kernel": 3, "stride": 2},
        {"name": "Block3", "in_ch": 24, "out_ch": 24, "kernel": 3, "stride": 1},
        {"name": "Block4", "in_ch": 24, "out_ch": 24, "kernel": 3, "stride": 1},
        {"name": "Block5", "in_ch": 24, "out_ch": 40, "kernel": 5, "stride": 2},
        {"name": "Block6", "in_ch": 40, "out_ch": 40, "kernel": 5, "stride": 1},
        {"name": "Block7", "in_ch": 40, "out_ch": 40, "kernel": 5, "stride": 1},
        {"name": "Block8", "in_ch": 40, "out_ch": 80, "kernel": 3, "stride": 2},
        {"name": "Block9", "in_ch": 80, "out_ch": 80, "kernel": 3, "stride": 1},
        {"name": "Block10", "in_ch": 80, "out_ch": 80, "kernel": 3, "stride": 1},
        {"name": "Block11", "in_ch": 80, "out_ch": 80, "kernel": 3, "stride": 1},
        {"name": "Block12", "in_ch": 80, "out_ch": 80, "kernel": 3, "stride": 1},
        {"name": "Block13", "in_ch": 80, "out_ch": 112, "kernel": 5, "stride": 1},
        {"name": "Block14", "in_ch": 112, "out_ch": 112, "kernel": 5, "stride": 1},
        {"name": "Block15", "in_ch": 112, "out_ch": 112, "kernel": 5, "stride": 1},
        {"name": "Block16", "in_ch": 112, "out_ch": 192, "kernel": 5, "stride": 2},
        {"name": "Block17", "in_ch": 192, "out_ch": 192, "kernel": 5, "stride": 1},
        {"name": "Block18", "in_ch": 192, "out_ch": 192, "kernel": 5, "stride": 1},
        {"name": "Block19", "in_ch": 192, "out_ch": 192, "kernel": 5, "stride": 1},
        {"name": "Block20", "in_ch": 192, "out_ch": 192, "kernel": 5, "stride": 1},
        {"name": "Block21", "in_ch": 192, "out_ch": 192, "kernel": 5, "stride": 1},
        {"name": "Block22", "in_ch": 192, "out_ch": 320, "kernel": 3, "stride": 1},
        {"name": "Head", "in_ch": 320, "out_ch": 1280, "kernel": 1, "stride": 1},
    ]

    print(f"Current Implementation: {len(current_blocks)} layers")
    for i, block in enumerate(current_blocks):
        print(f"  {i:2d}. {block['name']:8s}: {block['in_ch']:3d}->{block['out_ch']:3d}, k={block['kernel']}, s={block['stride']}")

    print(f"\nReal EfficientNet-B1 from timm:")
    if hasattr(model, 'blocks'):
        print(f"  Total blocks: {len(model.blocks)}")

        # 分析真实模型的每个block
        for i, block in enumerate(model.blocks):
            block_info = analyze_block_structure(block)
            if block_info:
                print(f"  {i:2d}. Block{i:2d}: {block_info}")

    # 对比总结
    print(f"\n=== Comparison Summary ===")
    if hasattr(model, 'blocks'):
        real_blocks = len(model.blocks)
        current_blocks_count = len(current_blocks) - 2  # 减去stem和head
        print(f"Real model blocks: {real_blocks}")
        print(f"Current implementation blocks: {current_blocks_count}")
        if real_blocks == current_blocks_count:
            print("✅ Block count matches!")
        else:
            print(f"❌ Block count mismatch: {real_blocks} vs {current_blocks_count}")

def analyze_block_structure(block):
    """分析单个block的结构"""
    try:
        info_parts = []

        # 尝试获取输入输出通道数
        for name, module in block.named_modules():
            if hasattr(module, 'in_channels') and hasattr(module, 'out_channels'):
                in_ch = module.in_channels
                out_ch = module.out_channels

                if hasattr(module, 'kernel_size'):
                    kernel = module.kernel_size[0] if isinstance(module.kernel_size, tuple) else module.kernel_size
                    stride = getattr(module, 'stride', 1)
                    stride = stride[0] if isinstance(stride, tuple) else stride

                    info_parts.append(f"{in_ch}->{out_ch}(k{kernel}s{stride})")
                else:
                    info_parts.append(f"{in_ch}->{out_ch}")

                if len(info_parts) >= 3:  # 限制输出长度
                    break

        return " | ".join(info_parts) if info_parts else "Unknown structure"
    except:
        return "Analysis failed"

if __name__ == "__main__":
    analyze_efficientnet_b1()
