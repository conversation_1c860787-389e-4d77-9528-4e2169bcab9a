#!/bin/bash

# EfficientNet Android Deployment Script
# 部署EfficientNet训练程序到Android设备

set -e

echo "=== EfficientNet Android Deployment Script ==="

# 设置路径
BUILD_DIR="../../project/android/build_64"
DEVICE_DIR="/data/local/tmp/efficientnet_training"
EFFICIENTNET_DIR="$(pwd)"

echo "Build Directory: $BUILD_DIR"
echo "Device Directory: $DEVICE_DIR"
echo "EfficientNet Directory: $EFFICIENTNET_DIR"

# 检查ADB连接
echo ""
echo "=== Checking ADB Connection ==="
if ! adb devices | grep -q "device$"; then
    echo "❌ Error: No Android device connected via ADB"
    echo "Please connect your Android device and enable USB debugging"
    exit 1
fi

DEVICE_MODEL=$(adb shell getprop ro.product.model 2>/dev/null | tr -d '\r')
echo "✅ Connected to device: $DEVICE_MODEL"

# 检查adb连接
if ! command -v adb &> /dev/null; then
    echo "Error: adb not found. Please install Android SDK platform-tools"
    exit 1
fi

# 检查设备连接
echo "Checking device connection..."
DEVICE_COUNT=$(adb devices | grep -c "device$" || true)
if [ "$DEVICE_COUNT" -eq 0 ]; then
    echo "Error: No Android device connected"
    echo "Please connect an Android device with USB debugging enabled"
    exit 1
fi

echo "Connected device:"
adb devices

# 检查构建文件
echo ""
echo "=== Checking Build Files ==="
if [ ! -f "$BUILD_DIR/runTrainDemo.out" ]; then
    echo "Error: Training program not found. Please run 1_build_efficientnet_android.sh first"
    exit 1
fi
echo "✅ Found: runTrainDemo.out"

echo ""
echo "=== Deploying EfficientNet Training to Android Device ==="

# 创建设备目录
echo "Creating directories on device..."
adb shell "mkdir -p $DEVICE_DIR/models"
adb shell "mkdir -p $DEVICE_DIR/real_test_data/train"

# 推送训练程序
echo "Pushing training binary..."
adb push $BUILD_DIR/runTrainDemo.out $DEVICE_DIR/
adb shell "chmod +x $DEVICE_DIR/runTrainDemo.out"

# 推送MNN库文件
echo "Pushing MNN libraries..."
adb push $BUILD_DIR/libMNN.so $DEVICE_DIR/
adb push $BUILD_DIR/libMNN_Express.so $DEVICE_DIR/
adb push $BUILD_DIR/libMNN_CL.so $DEVICE_DIR/

# 推送训练库
echo "Pushing training libraries..."
adb push $BUILD_DIR/tools/train/libMNNTrainUtils.so $DEVICE_DIR/
adb push $BUILD_DIR/tools/train/libMNNTrain.so $DEVICE_DIR/

# 推送transformer工具
if [ -f "$BUILD_DIR/transformer" ]; then
    echo "Pushing transformer tool..."
    adb push $BUILD_DIR/transformer $DEVICE_DIR/
    adb shell "chmod +x $DEVICE_DIR/transformer"
fi

# 推送EfficientNet模型文件
echo "Pushing EfficientNet model files..."
if [ -f "efficientnet_train_mse.mnn" ]; then
    adb push efficientnet_train_mse.mnn $DEVICE_DIR/
fi

# 推送测试数据（如果存在）
if [ -d "real_test_data" ]; then
    echo "Pushing test data..."
    adb push real_test_data/ $DEVICE_DIR/
fi

# 设置执行权限
echo "Setting permissions..."
adb shell "chmod -R 755 $DEVICE_DIR"

echo ""
echo "=== Deployment Summary ==="
echo "✅ EfficientNet training program deployed successfully!"
echo ""
echo "Device directory: $DEVICE_DIR"
echo ""
echo "Deployed files:"
echo "  📱 runTrainDemo.out (Training executable)"
echo "  📚 libMNN*.so (MNN libraries)"
echo "  🧠 efficientnet_*.mnn (EfficientNet models)"
echo "  🖼️  real_test_data/ (Test images)"
echo ""
echo "Available training demos:"
adb shell "cd $DEVICE_DIR && LD_LIBRARY_PATH=. ./runTrainDemo.out | grep EfficientNet" || echo "  - EfficientNetMobileTraining"
echo ""
echo "Usage examples:"
echo "  # Test EfficientNet CPU training:"
echo "  adb shell 'cd $DEVICE_DIR && LD_LIBRARY_PATH=. ./runTrainDemo.out EfficientNetMobileTraining ./real_test_data/train/ ./real_test_data/train.txt'"
echo ""
echo "  # Check device info:"
echo "  adb shell 'cd $DEVICE_DIR && cat /proc/cpuinfo | grep -E \"processor|model name\" | head -8'"
echo ""
echo "Deployment completed successfully! 🚀"
