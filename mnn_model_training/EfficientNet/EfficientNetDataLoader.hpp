//
//  EfficientNetDataLoader.hpp
//  MNN
//
//  Created for EfficientNet Training
//

#ifndef EfficientNetDataLoader_hpp
#define EfficientNetDataLoader_hpp

#include <MNN/expr/Module.hpp>
#include <string>
#include "ImageDataset.hpp"
#include "DataLoader.hpp"

using namespace MNN;
using namespace MNN::Express;
using namespace MNN::Train;

class EfficientNetDataLoader {
public:
    static std::shared_ptr<ImageDataset::ImageConfig> createConfig() {
        // EfficientNet-B5 使用 RGB 格式，456x456 输入尺寸
        auto converImagesToFormat = CV::RGB;
        int resizeHeight = 456;
        int resizeWidth = 456;
        
        // ImageNet 标准化参数
        // mean = [0.485, 0.456, 0.406], std = [0.229, 0.224, 0.225]
        std::vector<float> means = {0.485f * 255.0f, 0.456f * 255.0f, 0.406f * 255.0f};
        std::vector<float> scales = {1.0f / (0.229f * 255.0f), 1.0f / (0.224f * 255.0f), 1.0f / (0.225f * 255.0f)};
        
        // 不进行裁剪，直接resize到目标尺寸
        std::vector<float> cropFraction = {1.0f, 1.0f};
        bool centerOrRandomCrop = false; // false表示中心裁剪
        
        return std::shared_ptr<ImageDataset::ImageConfig>(
            ImageDataset::ImageConfig::create(converImagesToFormat, resizeHeight, resizeWidth, 
                                            scales, means, cropFraction, centerOrRandomCrop)
        );
    }
    
    static DatasetPtr createDataset(const std::string& trainImagesFolder, 
                                   const std::string& trainImagesTxt,
                                   bool readAllImagesToMemory = false) {
        auto config = createConfig();
        return ImageDataset::create(trainImagesFolder, trainImagesTxt, config.get(), readAllImagesToMemory);
    }
    
    static std::shared_ptr<DataLoader> createDataLoader(const std::string& trainImagesFolder,
                                                       const std::string& trainImagesTxt,
                                                       int batchSize = 1,
                                                       bool shuffle = true,
                                                       bool readAllImagesToMemory = false) {
        auto dataset = createDataset(trainImagesFolder, trainImagesTxt, readAllImagesToMemory);
        auto dataLoader = std::shared_ptr<DataLoader>(dataset->createLoader(batchSize, shuffle));
        return dataLoader;
    }
    
    // EfficientNet-B5 特定的输入尺寸信息
    static constexpr int INPUT_HEIGHT = 456;
    static constexpr int INPUT_WIDTH = 456;
    static constexpr int INPUT_CHANNELS = 3;
};

#endif /* EfficientNetDataLoader_hpp */
