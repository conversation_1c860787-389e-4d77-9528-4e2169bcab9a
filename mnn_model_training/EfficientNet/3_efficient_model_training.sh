#!/bin/bash

# EfficientNet Mobile Training Execution Script
# 在Android设备上执行EfficientNet训练 - 支持多种batch size测试
#
# Usage:
#   ./3_training.sh                    # Run all batch sizes (1,4,8,16,32,64)
#   ./3_training.sh all                # Run all batch sizes (same as above)
#   ./3_training.sh 1 4 8              # Run specific batch sizes
#   ./3_training.sh 16                 # Run single batch size
#
# Supported batch sizes: 1, 4, 8, 16, 32, 64

set -e

# 显示帮助信息
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "EfficientNet Mobile Training Script"
    echo ""
    echo "Usage:"
    echo "  ./3_training.sh                    # Run all batch sizes (1,4,8,16,32,64)"
    echo "  ./3_training.sh all                # Run all batch sizes (same as above)"
    echo "  ./3_training.sh 1 4 8              # Run specific batch sizes"
    echo "  ./3_training.sh 16                 # Run single batch size"
    echo "  ./3_training.sh -h, --help         # Show this help"
    echo ""
    echo "Supported batch sizes: 1, 4, 8, 16, 32, 64"
    echo ""
    echo "Examples:"
    echo "  ./3_training.sh                    # Test all batch sizes"
    echo "  ./3_training.sh 1 4                # Test only batch size 1 and 4"
    echo "  ./3_training.sh 32                 # Test only batch size 32"
    echo ""
    exit 0
fi

echo "=== EfficientNet Mobile Training Execution ==="

# 设备路径
DEVICE_DIR="/data/local/tmp/efficientnet_training"

echo "Device Directory: $DEVICE_DIR"

# 检查ADB连接
echo ""
echo "=== Checking ADB Connection ==="
if ! adb devices | grep -q "device$"; then
    echo "❌ Error: No Android device connected via ADB"
    echo "Please connect your Android device and enable USB debugging"
    exit 1
fi

# 获取设备信息
DEVICE_MODEL=$(adb shell getprop ro.product.model 2>/dev/null | tr -d '\r' || echo "Unknown Device")
echo "✅ Connected to device: $DEVICE_MODEL"

# 支持的batch size列表
BATCH_SIZES=(1 4 8 16 32 64)

echo ""
echo "=== EfficientNet Training Configuration ==="
echo "  Model: EfficientNet-B1 (Real Architecture)"
echo "  Input Size: 240x240x3"
echo "  Backend: GPU (OpenCL)"
echo "  Precision: FP32"
echo "  Learning Rate: Constant (0.001)"
echo "  Loss Function: MSE"
echo "  Supported Batch Sizes: ${BATCH_SIZES[*]}"
echo ""

# 解析命令行参数
if [ $# -eq 0 ]; then
    echo "=== Running All Batch Size Tests ==="
    RUN_ALL=true
    SELECTED_BATCH_SIZES=("${BATCH_SIZES[@]}")
elif [ "$1" = "all" ]; then
    echo "=== Running All Batch Size Tests ==="
    RUN_ALL=true
    SELECTED_BATCH_SIZES=("${BATCH_SIZES[@]}")
else
    echo "=== Running Selected Batch Size Tests ==="
    RUN_ALL=false
    SELECTED_BATCH_SIZES=("$@")

    # 验证输入的batch size是否支持
    for batch_size in "${SELECTED_BATCH_SIZES[@]}"; do
        if [[ ! " ${BATCH_SIZES[*]} " =~ " ${batch_size} " ]]; then
            echo "❌ Error: Unsupported batch size: $batch_size"
            echo "Supported batch sizes: ${BATCH_SIZES[*]}"
            exit 1
        fi
    done
fi

echo "Selected batch sizes: ${SELECTED_BATCH_SIZES[*]}"
echo ""

# 执行所有选定的batch size训练
TOTAL_TESTS=${#SELECTED_BATCH_SIZES[@]}
SUCCESSFUL_TESTS=0
FAILED_TESTS=0

echo "=== Starting Training Tests ==="
echo "Total tests to run: $TOTAL_TESTS"
echo ""

# 创建结果日志文件
RESULTS_LOG="efficientnet_batch_results_$(date +%Y%m%d_%H%M%S).log"
echo "EfficientNet Batch Size Performance Results" > "$RESULTS_LOG"
echo "Generated on: $(date)" >> "$RESULTS_LOG"
echo "Device: $DEVICE_MODEL" >> "$RESULTS_LOG"
echo "=========================================" >> "$RESULTS_LOG"
echo "" >> "$RESULTS_LOG"

for i in "${!SELECTED_BATCH_SIZES[@]}"; do
    batch_size=${SELECTED_BATCH_SIZES[$i]}
    test_num=$((i + 1))

    echo "--- Test $test_num/$TOTAL_TESTS: Batch Size $batch_size ---"
    echo "Test $test_num/$TOTAL_TESTS: Batch Size $batch_size" >> "$RESULTS_LOG"
    echo "Start time: $(date)" >> "$RESULTS_LOG"

    # 执行训练命令
    echo "Executing: cd $DEVICE_DIR && LD_LIBRARY_PATH=.:/vendor/lib64 ./runTrainDemo.out EfficientNetGeneratedTrain ./real_test_data/train/ ./real_test_data/train.txt 3 $batch_size"
    echo ""

    # 记录开始时间
    start_time=$(date +%s)

    # 执行训练并捕获输出
    if adb shell "cd $DEVICE_DIR && LD_LIBRARY_PATH=.:/vendor/lib64 ./runTrainDemo.out EfficientNetGeneratedTrain ./real_test_data/train/ ./real_test_data/train.txt 3 $batch_size" 2>&1 | tee -a "$RESULTS_LOG"; then
        # 计算耗时
        end_time=$(date +%s)
        duration=$((end_time - start_time))

        echo "✅ Batch size $batch_size completed successfully in ${duration}s" | tee -a "$RESULTS_LOG"
        SUCCESSFUL_TESTS=$((SUCCESSFUL_TESTS + 1))
    else
        end_time=$(date +%s)
        duration=$((end_time - start_time))

        echo "❌ Batch size $batch_size failed after ${duration}s" | tee -a "$RESULTS_LOG"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    echo "End time: $(date)" >> "$RESULTS_LOG"
    echo "Duration: ${duration}s" >> "$RESULTS_LOG"
    echo "----------------------------------------" >> "$RESULTS_LOG"
    echo ""

    # 如果不是最后一个测试，等待一下让设备降温
    if [ $test_num -lt $TOTAL_TESTS ]; then
        echo "Waiting 10 seconds for device cooling..."
        sleep 10
    fi
done

echo ""
echo "=== All Training Tests Completed ==="
echo "Summary:" | tee -a "$RESULTS_LOG"
echo "  Total tests: $TOTAL_TESTS" | tee -a "$RESULTS_LOG"
echo "  Successful: $SUCCESSFUL_TESTS" | tee -a "$RESULTS_LOG"
echo "  Failed: $FAILED_TESTS" | tee -a "$RESULTS_LOG"
echo "  Results saved to: $RESULTS_LOG" | tee -a "$RESULTS_LOG"

if [ $SUCCESSFUL_TESTS -eq $TOTAL_TESTS ]; then
    echo ""
    echo "🎉 All EfficientNet batch size tests completed successfully!"
    exit 0
else
    echo ""
    echo "⚠️  Some tests failed. Check the results log for details."
    exit 1
fi
