#!/usr/bin/env python3
"""
EfficientNet Test Data Generation Script
生成用于EfficientNet训练的真实测试图像数据
"""

import os
import sys
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import random

def create_test_image(width, height, class_id, image_id):
    """
    创建一个测试图像
    
    Args:
        width: 图像宽度
        height: 图像高度  
        class_id: 类别ID
        image_id: 图像ID
    
    Returns:
        PIL Image对象
    """
    # 为不同类别创建不同的颜色模式
    colors = [
        (255, 100, 100),  # 红色系
        (100, 255, 100),  # 绿色系
        (100, 100, 255),  # 蓝色系
        (255, 255, 100),  # 黄色系
        (255, 100, 255),  # 紫色系
        (100, 255, 255),  # 青色系
        (255, 200, 100),  # 橙色系
        (200, 100, 255),  # 紫红色系
        (100, 200, 255),  # 天蓝色系
        (200, 255, 100),  # 浅绿色系
    ]
    
    # 选择基础颜色
    base_color = colors[class_id % len(colors)]
    
    # 创建图像
    image = Image.new('RGB', (width, height), base_color)
    draw = ImageDraw.Draw(image)
    
    # 添加一些随机图案使图像更真实
    for _ in range(20):
        # 随机矩形
        x1 = random.randint(0, width-50)
        y1 = random.randint(0, height-50)
        x2 = x1 + random.randint(20, 50)
        y2 = y1 + random.randint(20, 50)
        
        # 随机颜色变化
        color_variation = tuple(max(0, min(255, c + random.randint(-50, 50))) for c in base_color)
        draw.rectangle([x1, y1, x2, y2], fill=color_variation)
    
    # 添加一些圆形
    for _ in range(10):
        x = random.randint(0, width-30)
        y = random.randint(0, height-30)
        r = random.randint(10, 30)
        
        color_variation = tuple(max(0, min(255, c + random.randint(-30, 30))) for c in base_color)
        draw.ellipse([x, y, x+r, y+r], fill=color_variation)
    
    # 添加文本标识
    try:
        # 尝试使用系统字体
        font_size = min(width, height) // 20
        font = ImageFont.load_default()
    except:
        font = None
    
    text = f"Class{class_id}_Img{image_id}"
    text_color = tuple(255 - c for c in base_color)  # 反色文本
    
    # 计算文本位置（居中）
    if font:
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
    else:
        text_width = len(text) * 6  # 估算宽度
        text_height = 11  # 估算高度
    
    text_x = (width - text_width) // 2
    text_y = (height - text_height) // 2
    
    draw.text((text_x, text_y), text, fill=text_color, font=font)
    
    return image

def create_test_dataset(target_iterations=50, max_batch_size=64, epochs=2):
    """创建EfficientNet测试数据集

    Args:
        target_iterations: 目标每个epoch的iteration数量
        max_batch_size: 支持的最大batch size
        epochs: 训练epoch数量
    """

    print("=== EfficientNet Test Data Generation ===")
    print("Creating realistic test images for EfficientNet-B1 training...")
    print()

    # EfficientNet-B1 配置
    IMAGE_WIDTH = 240
    IMAGE_HEIGHT = 240
    NUM_CLASSES = 5

    # 计算需要的图片数量
    # 为了让所有batch size都有相同的iteration数量
    # 我们生成足够支持最大batch size的图片数量
    # 但训练时通过限制数据集大小来控制iteration数量
    TOTAL_IMAGES_NEEDED = target_iterations * max_batch_size
    IMAGES_PER_CLASS = TOTAL_IMAGES_NEEDED // NUM_CLASSES

    print(f"Configuration:")
    print(f"  Target iterations per epoch: {target_iterations}")
    print(f"  Maximum batch size: {max_batch_size}")
    print(f"  Number of epochs: {epochs}")
    print(f"  Total images generated: {TOTAL_IMAGES_NEEDED}")
    print(f"  Images per class: {IMAGES_PER_CLASS}")
    print()

    # 验证batch size组合 - 所有batch size都将有相同的iteration数量
    supported_batch_sizes = [1, 4, 8, 16, 32, 64]
    print("Training strategy:")
    print(f"  All batch sizes will train for exactly {target_iterations} iterations per epoch")
    print("  Data usage per batch size:")
    for batch_size in supported_batch_sizes:
        if batch_size <= max_batch_size:
            images_used = target_iterations * batch_size
            usage_percent = (images_used / TOTAL_IMAGES_NEEDED) * 100
            print(f"    Batch size {batch_size:2d}: uses {images_used:4d}/{TOTAL_IMAGES_NEEDED} images ({usage_percent:.1f}%)")
    print()
    
    # 创建目录结构
    base_dir = "real_test_data"
    train_dir = os.path.join(base_dir, "train")
    
    print(f"Creating directory structure...")
    os.makedirs(train_dir, exist_ok=True)
    print(f"✅ Created: {train_dir}")
    
    # 生成图像和标签文件
    train_list = []
    total_images = 0
    
    print(f"\nGenerating test images...")
    print(f"Configuration:")
    print(f"  Image Size: {IMAGE_WIDTH}x{IMAGE_HEIGHT}")
    print(f"  Classes: {NUM_CLASSES}")
    print(f"  Images per class: {IMAGES_PER_CLASS}")
    print(f"  Total images: {NUM_CLASSES * IMAGES_PER_CLASS}")
    print()
    
    for class_id in range(NUM_CLASSES):
        print(f"Generating class {class_id}...")
        
        for img_id in range(IMAGES_PER_CLASS):
            # 创建图像
            image = create_test_image(IMAGE_WIDTH, IMAGE_HEIGHT, class_id, img_id)
            
            # 保存图像
            filename = f"class{class_id:02d}_img{img_id:03d}.jpg"
            filepath = os.path.join(train_dir, filename)
            
            # 保存为JPEG格式，质量95%
            image.save(filepath, "JPEG", quality=95)
            
            # 添加到训练列表
            train_list.append(f"{filename} {class_id}")
            total_images += 1
            
            print(f"  ✅ Created: {filename} (class {class_id})")
    
    # 保存训练列表文件
    train_list_file = os.path.join(base_dir, "train.txt")
    with open(train_list_file, 'w') as f:
        for item in train_list:
            f.write(item + '\n')
    
    print(f"\n✅ Training list saved: {train_list_file}")
    
    # 验证生成的数据
    print(f"\n=== Data Verification ===")
    print(f"Total images generated: {total_images}")
    print(f"Training directory: {train_dir}")
    print(f"Training list file: {train_list_file}")
    
    # 检查文件大小
    total_size = 0
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            filepath = os.path.join(root, file)
            total_size += os.path.getsize(filepath)
    
    print(f"Total dataset size: {total_size / (1024*1024):.2f} MB")
    
    # 显示目录结构
    print(f"\nGenerated directory structure:")
    for root, dirs, files in os.walk(base_dir):
        level = root.replace(base_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files[:5]:  # 只显示前5个文件
            print(f"{subindent}{file}")
        if len(files) > 5:
            print(f"{subindent}... and {len(files)-5} more files")
    
    print(f"\n🎉 EfficientNet test data generation completed successfully!")
    print(f"\nNext steps:")
    print(f"  1. Run: ./1_build_efficientnet_android.sh")
    print(f"  2. Run: ./2_deploy_efficientnet_android.sh")
    print(f"  3. Run: ./3_training.sh")
    
    return True

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Generate test images for EfficientNet training')
    parser.add_argument('--iterations', type=int, default=20,
                       help='Target iterations per epoch (default: 20)')
    parser.add_argument('--max-batch-size', type=int, default=64,
                       help='Maximum batch size to support (default: 64)')
    parser.add_argument('--epochs', type=int, default=2,
                       help='Number of epochs (default: 2)')

    args = parser.parse_args()

    try:
        success = create_test_dataset(
            target_iterations=args.iterations,
            max_batch_size=args.max_batch_size,
            epochs=args.epochs
        )
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
