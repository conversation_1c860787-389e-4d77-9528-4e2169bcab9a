# EfficientNet Mobile Training

EfficientNet模型在Android设备上的GPU训练实现。

## 📁 目录结构

```
EfficientNet/
├── README.md                           # 本文档
├── 0_generate_training_model.sh        # 生成训练模型脚本
├── 1_build_efficientnet_android.sh     # Android编译脚本
├── 2_deploy_efficientnet_android.sh    # Android部署脚本
├── 3_training.sh                       # 训练执行脚本
├── EfficientNetMobileDemo.cpp          # EfficientNet训练代码（本地副本）
├── EfficientNetDataLoader.hpp          # 数据加载器
├── create_real_test_images.py          # 测试数据生成脚本
├── efficientnet_b5_batch16_imgsz456.mnn # EfficientNet推理模型
├── efficientnet_train_config.json      # 训练配置文件
└── efficientnet_revert_mse.json        # 模型转换配置

编译时使用的代码位置：
└── ../../tools/train/source/demo/EfficientNetMobileDemo.cpp  # 编译时的实际位置
```

## 🚀 快速开始

### 1. 生成训练模型

```bash
cd /path/to/mnn/mnn_model_training/EfficientNet
chmod +x 0_generate_training_model.sh
./0_generate_training_model.sh
```

### 2. 生成测试数据

```bash
python3 create_real_test_images.py
```

### 3. 编译EfficientNet训练程序

```bash
chmod +x 1_build_efficientnet_android.sh
./1_build_efficientnet_android.sh
```

### 4. 部署到Android设备

```bash
chmod +x 2_deploy_efficientnet_android.sh
./2_deploy_efficientnet_android.sh
```

### 5. 在Android设备上运行训练

```bash
# 连接到设备
adb shell

# 进入训练目录
cd /data/local/tmp/efficientnet_training

# 运行EfficientNet GPU训练
LD_LIBRARY_PATH=. ./runTrainDemo.out EfficientNetMobileTraining ./real_test_data/train/ ./real_test_data/train.txt
```

## 🔧 技术特性

### GPU训练支持
- ✅ **OpenCL GPU后端**：使用手机GPU进行训练加速
- ✅ **FP32精度**：高精度浮点运算确保训练质量
- ✅ **ARM优化**：支持ARM64架构和高级指令集

### 训练配置
- **批次大小**：1（适合移动设备内存限制）
- **学习率**：0.0001（适合移动端训练）
- **优化器**：ADAM
- **损失函数**：MSE（均方误差）
- **输入尺寸**：456x456（EfficientNet-B5标准尺寸）

### 设备要求
- Android 5.0+ (API 21+)
- ARM64架构处理器
- 支持OpenCL的GPU
- 至少4GB RAM

## 📊 性能表现

### 预期性能（基于ConvNext经验）
- **训练速度**：~15-20秒/epoch（10个样本，EfficientNet-B5）
- **内存使用**：稳定，无内存泄漏
- **模型大小**：~1.2GB训练模型

### 训练结果示例
```
=== EfficientNet Mobile Training Demo ===
Using GPU (OpenCL) backend for training with FP32 precision

--- Epoch 1/3 ---
Iteration 0/10, Loss: 1200.5, LR: 0.0001
Epoch 1 completed in 18 seconds. Average loss: 1200.5

--- Epoch 2/3 ---
Iteration 0/10, Loss: 1150.85, LR: 9e-05
Epoch 2 completed in 18 seconds. Average loss: 1050.378

--- Epoch 3/3 ---
Iteration 0/10, Loss: 950.937, LR: 8.1e-05
Epoch 3 completed in 18 seconds. Average loss: 920.105

=== Training Completed ===
Final model saved: efficientnet_mobile_final.mnn
```

## 🛠️ 开发说明

### 代码文件说明

1. **EfficientNetMobileDemo.cpp**
   - 主要的EfficientNet训练实现
   - 集成到MNN训练框架中
   - 支持GPU训练和FP32精度
   - 针对EfficientNet-B5架构优化

2. **EfficientNetDataLoader.hpp**
   - EfficientNet专用数据加载器
   - 支持456x456输入尺寸
   - ImageNet标准化参数

### 编译选项

```cmake
-DMNN_BUILD_TRAIN=ON          # 启用训练功能
-DMNN_BUILD_TRAIN_MINI=ON     # 启用轻量训练
-DMNN_OPENCL=ON               # 启用OpenCL GPU支持
-DMNN_ARM82=ON                # 启用ARM高级指令集
```

### 关键配置

```cpp
// GPU配置
MNNForwardType backend = MNN_FORWARD_OPENCL;
BackendConfig config;
config.precision = BackendConfig::Precision_High; // FP32
config.power = BackendConfig::Power_High;         // 高性能模式
```

## 🔍 故障排除

### 常见问题

1. **Segmentation Fault**
   - 检查图像数据格式是否正确（需要真实JPEG文件）
   - 确保模型文件路径正确
   - 验证设备内存是否充足

2. **GPU初始化失败**
   - 检查设备是否支持OpenCL
   - 确认libMNN_CL.so已正确部署
   - 尝试降低模型复杂度

3. **编译错误**
   - 检查Android NDK路径设置
   - 确认CMake版本兼容性
   - 验证依赖库是否完整

### 调试技巧

```bash
# 检查设备GPU信息
adb shell "cat /proc/cpuinfo | grep -E 'processor|model name'"

# 检查内存使用
adb shell "cat /proc/meminfo | grep MemAvailable"

# 查看训练日志
adb shell "cd /data/local/tmp/efficientnet_training && LD_LIBRARY_PATH=. ./runTrainDemo.out EfficientNetMobileTraining ./real_test_data/train/ ./real_test_data/train.txt 2>&1 | tee training.log"
```

## 📈 未来改进

- [ ] 支持更多EfficientNet变体（B0-B7）
- [ ] 实现量化感知训练
- [ ] 添加训练进度可视化
- [ ] 支持联邦学习
- [ ] 优化内存使用和训练速度
- [ ] 支持混合精度训练

## 📄 许可证

本项目遵循MNN项目的许可证条款。
