//
//  EfficientNetGeneratedDemo.cpp
//  MNN
//
//  EfficientNet Mobile Training Demo using Generated Express Code
//

#include <MNN/expr/Executor.hpp>
#include <MNN/expr/Optimizer.hpp>
#include <MNN/expr/Module.hpp>
#include <cmath>
#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <memory>
#include <chrono>
#include <algorithm>
#include <thread>
#include "DemoUnit.hpp"
#include "ImageDataset.hpp"
#include "DataLoader.hpp"
#include "ADAM.hpp"
#include "SGD.hpp"
#include "LearningRateScheduler.hpp"
#include "Loss.hpp"
#include "RandomGenerator.hpp"
#include "NN.hpp"
#include "Transformer.hpp"
#include "module/PipelineModule.hpp"
#include "EfficientNetB1Model.hpp"

using namespace MNN;
using namespace MNN::Express;
using namespace MNN::Train;
using namespace std;
using namespace std::chrono;

// 常量定义
const int INPUT_SIZE = 240;  // EfficientNet-B1输入尺寸
const int NUM_CLASSES = 5;   // 类别数

// EfficientNet-B1模型现在在独立的头文件中定义

// EfficientNet GPU训练工具类 - 优化为FP32+MSE+计时
class EfficientNetGPUTrainer {
public:
    static void train(MNNForwardType backend, int threadNumber, std::shared_ptr<Module> model,
                     const int numClasses, const int addToLabel,
                     std::string trainImagesFolder, std::string trainImagesTxt,
                     std::string testImagesFolder, std::string testImagesTxt, int size = INPUT_SIZE, int batchSize = 1) {

        // 配置执行器 - 平衡性能和热节流
        auto exe = Executor::getGlobalExecutor();
        BackendConfig config;
        config.precision = BackendConfig::Precision_High; // FP32精度
        config.power = BackendConfig::Power_Normal; // 普通功耗模式（避免过热）
        exe->setGlobalExecutorConfig(backend, config, threadNumber);

        cout << "=== GPU Training Configuration ===" << endl;
        cout << "Backend: " << (backend == MNN_FORWARD_OPENCL ? "GPU (OpenCL)" : "CPU") << endl;
        cout << "Precision: FP32" << endl;
        cout << "Loss Function: MSE (Mean Squared Error)" << endl;
        cout << "Batch Size: " << batchSize << endl;
        cout << "Learning Rate: Constant (0.001)" << endl;
        cout << "Focus: Performance Testing (not accuracy)" << endl;
        cout << "=================================" << endl;
        
        // 使用ADAM优化器
        std::shared_ptr<SGD> solver(new ADAM(model));
        solver->setMomentum(0.9f);
        solver->setWeightDecay(0.00004f);

        // 数据预处理配置 - 参考MobilenetV2
        auto converImagesToFormat = CV::RGB;
        int resizeHeight = size;
        int resizeWidth = size;
        std::vector<float> means = {127.5f, 127.5f, 127.5f};
        std::vector<float> scales = {1/127.5f, 1/127.5f, 1/127.5f};
        std::vector<float> cropFraction = {0.875f, 0.875f}; // center crop fraction
        bool centerOrRandomCrop = false; // true for random crop
        
        std::shared_ptr<ImageDataset::ImageConfig> datasetConfig(
            ImageDataset::ImageConfig::create(converImagesToFormat, resizeHeight, resizeWidth, 
                                             scales, means, cropFraction, centerOrRandomCrop));
        
        bool readAllImagesToMemory = false;
        auto trainDataset = ImageDataset::create(trainImagesFolder, trainImagesTxt, datasetConfig.get(), readAllImagesToMemory);
        auto testDataset = ImageDataset::create(testImagesFolder, testImagesTxt, datasetConfig.get(), readAllImagesToMemory);

        // 训练配置 - 支持可变batch size
        const int trainBatchSize = batchSize;  // 使用传入的batch size
        const int trainNumWorkers = 0; // 移动设备不使用多线程
        const int testBatchSize = batchSize;   // 测试也使用相同的batch size
        const int testNumWorkers = 0;

        auto trainDataLoader = trainDataset.createLoader(trainBatchSize, true, true, trainNumWorkers);
        auto testDataLoader = testDataset.createLoader(testBatchSize, true, false, testNumWorkers);

        // 固定iteration数量，确保所有batch size都有相同的训练iteration
        const int FIXED_ITERATIONS_PER_EPOCH = 20;
        const int maxTrainIterations = trainDataLoader->iterNumber();
        const int maxTestIterations = testDataLoader->iterNumber();

        // 使用固定iteration数量或数据集最大iteration数量中的较小值
        const int trainIterations = (FIXED_ITERATIONS_PER_EPOCH < maxTrainIterations) ? FIXED_ITERATIONS_PER_EPOCH : maxTrainIterations;
        const int testIterations = (FIXED_ITERATIONS_PER_EPOCH < maxTestIterations) ? FIXED_ITERATIONS_PER_EPOCH : maxTestIterations;

        cout << "=== EfficientNet Transfer Learning Started ===" << endl;
        cout << "Backend: " << (backend == MNN_FORWARD_OPENCL ? "GPU (OpenCL)" : "CPU") << endl;
        cout << "Model: EfficientNet-B1 with transfer learning" << endl;
        cout << "Fixed iterations per epoch: " << trainIterations << endl;
        cout << "Max available iterations: " << maxTrainIterations << endl;
        cout << "Test iterations per epoch: " << testIterations << endl;

        // 训练循环 - 只训练2个epoch进行测试
        for (int epoch = 0; epoch < 2; ++epoch) {
            model->clearCache();
            std::cout << "\n--- Epoch " << (epoch + 1) << "/2 ---" << std::endl;
            
            // 训练阶段
            trainDataLoader->reset();
            model->setIsTraining(true);
            float epochLoss = 0.0f;
            
            for (int i = 0; i < trainIterations; i++) {
                // 开始计时
                auto iterStart = high_resolution_clock::now();

                auto trainData = trainDataLoader->next();
                auto example = trainData[0];

                // 计算One-Hot标签
                auto newTarget = _OneHot(_Cast<int32_t>(_Squeeze(example.second[0] + _Scalar<int32_t>(addToLabel), {})),
                                        _Scalar<int>(numClasses), _Scalar<float>(1.0f), _Scalar<float>(0.0f));

                // 前向传播
                auto predict = _Convert(model->forward(_Convert(example.first[0], NC4HW4)), NCHW);

                // 使用MSE损失函数而不是CrossEntropy
                auto loss = _SquaredDifference(predict, newTarget);
                loss = _ReduceMean(loss, {});

                // 恒定学习率（专注于性能测试而非精度）
                const float constantLearningRate = 0.0001f;
                solver->setLearningRate(constantLearningRate);

                // 反向传播和参数更新
                solver->step(loss);

                float currentLoss = loss->readMap<float>()[0];
                epochLoss += currentLoss;

                // 结束计时
                auto iterEnd = high_resolution_clock::now();
                auto iterDuration = duration_cast<milliseconds>(iterEnd - iterStart);

                // 每个iteration都打印耗时（支持batch size性能分析）
                cout << "train iteration: " << solver->currentStep();
                cout << " batch_size: " << batchSize;
                cout << " loss: " << currentLoss;
                cout << " lr: " << constantLearningRate;
                cout << " time: " << iterDuration.count() << "ms" << endl;

                // 热节流缓解已移除 - 专注于性能测试

                // 垃圾回收
                exe->gc(Executor::FULL);
            }
            
            float avgLoss = epochLoss / trainIterations;
            cout << "Epoch " << (epoch + 1) << " completed. Average loss: " << avgLoss << endl;

            // 简单的测试阶段（可选）
            if (testIterations > 0 && testIterations < 50) { // 只在测试数据不太多时进行测试
                int correct = 0;
                int sampleCount = 0;
                testDataLoader->reset();
                model->setIsTraining(false);

                for (int i = 0; i < min(testIterations, 20); i++) { // 最多测试20个样本
                    auto data = testDataLoader->next();
                    auto example = data[0];
                    auto predict = model->forward(_Convert(example.first[0], NC4HW4));
                    predict = _ArgMax(predict, 1);
                    auto label = _Squeeze(example.second[0]) + _Scalar<int32_t>(addToLabel);
                    sampleCount += label->getInfo()->size;
                    auto accu = _Cast<int32_t>(_Equal(predict, label).sum({}));
                    correct += accu->readMap<int32_t>()[0];
                }
                
                if (sampleCount > 0) {
                    float accuracy = float(correct) / sampleCount;
                    cout << "Test accuracy: " << correct << "/" << sampleCount
                         << " = " << (accuracy * 100) << "%" << endl;
                }
            }
        }

        cout << "\n=== EfficientNet GPU Training Completed ===" << endl;

        // 保存训练后的模型
        cout << "Saving trained model..." << endl;
        char modelPathBuffer[256];
        snprintf(modelPathBuffer, sizeof(modelPathBuffer), "efficientnet_b1_trained_batch%d.mnn", batchSize);

        // 使用模块的保存功能
        model->setIsTraining(false);
        Variable::save(model->parameters(), modelPathBuffer);
        cout << "✅ Model saved to: " << modelPathBuffer << endl;
    }
};

// EfficientNet生成代码训练Demo类
class EfficientNetGeneratedTrain : public DemoUnit {
public:
    virtual int run(int argc, const char* argv[]) override {
        if (argc < 3) {
            std::cout << "usage: ./runTrainDemo.out EfficientNetGeneratedTrain path/to/train/images/ path/to/train/image/txt [backend_type] [batch_size]" << std::endl;
            std::cout << "backend_type: 0=CPU, 3=GPU(OpenCL)" << std::endl;
            std::cout << "batch_size: 1,4,8,16,32,64 (default: 1)" << std::endl;
            return 0;
        }

        // 默认使用GPU训练，可通过参数指定
        MNNForwardType type = MNN_FORWARD_OPENCL;
        if (argc >= 4) {
            int backendType = atoi(argv[3]);
            if (backendType == 0) {
                type = MNN_FORWARD_CPU;
            }
        }

        // 解析batch size参数
        int batchSize = 1; // 默认batch size
        if (argc >= 5) {
            int inputBatchSize = atoi(argv[4]);
            // 验证batch size是否在支持的范围内
            std::vector<int> supportedBatchSizes = {1, 4, 8, 16, 32, 64};
            bool isSupported = false;
            for (int size : supportedBatchSizes) {
                if (inputBatchSize == size) {
                    batchSize = inputBatchSize;
                    isSupported = true;
                    break;
                }
            }
            if (!isSupported) {
                std::cout << "Warning: Unsupported batch size " << inputBatchSize
                         << ". Using default batch size 1." << std::endl;
                std::cout << "Supported batch sizes: 1, 4, 8, 16, 32, 64" << std::endl;
            }
        }
        
        // 设置随机种子
        RandomGenerator::generator(42);
        
        std::string trainImagesFolder = argv[1];
        std::string trainImagesTxt = argv[2];
        std::string testImagesFolder = argv[1]; // 使用相同的数据进行测试
        std::string testImagesTxt = argv[2];
        
        // 创建真正的EfficientNet-B1训练模型（使用独立的模型类）
        std::shared_ptr<Module> model(new EfficientNetB1Model(NUM_CLASSES));

        // 打印模型信息
        static_cast<EfficientNetB1Model*>(model.get())->printModelInfo();
        
        // 开始GPU训练（FP32 + MSE + 计时 + 可变batch size）
        EfficientNetGPUTrainer::train(type, 1, model, NUM_CLASSES, 0,
                                     trainImagesFolder, trainImagesTxt,
                                     testImagesFolder, testImagesTxt, INPUT_SIZE, batchSize);
        
        return 0;
    }
};

DemoUnitSetRegister(EfficientNetGeneratedTrain, "EfficientNetGeneratedTrain");
