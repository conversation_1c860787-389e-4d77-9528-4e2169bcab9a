#!/bin/bash

# YOLOv10 Training Execution Script
# 在Android设备上执行YOLOv10目标检测训练

set -e

echo "=== YOLOv10 Mobile Training Execution Script ==="

# 设置路径
DEVICE_PATH="/data/local/tmp/yolov10_training"
LOG_DIR="./training_logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo "Device Path: $DEVICE_PATH"
echo "Log Directory: $LOG_DIR"
echo "Timestamp: $TIMESTAMP"

# 创建本地日志目录
mkdir -p "$LOG_DIR"

# 检查ADB连接
echo ""
echo "=== Checking Android Device Connection ==="
if ! command -v adb &> /dev/null; then
    echo "❌ Error: ADB not found. Please install Android SDK platform-tools"
    exit 1
fi

DEVICE_COUNT=$(adb devices | grep -v "List of devices" | grep -c "device")
if [ $DEVICE_COUNT -eq 0 ]; then
    echo "❌ Error: No Android device connected"
    echo "Please connect an Android device and enable USB debugging"
    exit 1
fi

DEVICE_ID=$(adb devices | grep "device" | head -1 | awk '{print $1}')
echo "✅ Connected to device: $DEVICE_ID"

# 检查设备上的文件
echo ""
echo "=== Checking Device Files ==="
if adb shell "[ -f $DEVICE_PATH/runTrainDemo.out ]"; then
    echo "✅ Training executable found"
else
    echo "❌ Error: Training executable not found"
    echo "Please run ./2_deploy_yolov10_android.sh first"
    exit 1
fi

if adb shell "[ -d $DEVICE_PATH/data/images ]"; then
    echo "✅ Training data found"
else
    echo "❌ Error: Training data not found"
    echo "Please run python3 create_yolov10_test_data.py and redeploy"
    exit 1
fi

# 获取设备信息
echo ""
echo "=== Device Information ==="
DEVICE_MODEL=$(adb shell getprop ro.product.model | tr -d '\r')
ANDROID_VERSION=$(adb shell getprop ro.build.version.release | tr -d '\r')
CPU_ABI=$(adb shell getprop ro.product.cpu.abi | tr -d '\r')

echo "Device Model: $DEVICE_MODEL"
echo "Android Version: $ANDROID_VERSION"
echo "CPU Architecture: $CPU_ABI"

# 检查GPU支持
echo ""
echo "=== Checking GPU Support ==="
if adb shell "[ -f /system/lib64/libOpenCL.so ] || [ -f /vendor/lib64/libOpenCL.so ]"; then
    echo "✅ OpenCL GPU support detected"
    BACKEND=3
    BACKEND_NAME="GPU (OpenCL)"
else
    echo "⚠️  No OpenCL support detected, using CPU"
    BACKEND=0
    BACKEND_NAME="CPU"
fi

# 训练参数配置
echo ""
echo "=== Training Configuration ==="
BATCH_SIZES=(1 4 8 16)
TRAINING_MODE="single"  # single, batch, or performance

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --mode)
            TRAINING_MODE="$2"
            shift 2
            ;;
        --batch-size)
            BATCH_SIZES=($2)
            shift 2
            ;;
        --backend)
            if [ "$2" = "cpu" ]; then
                BACKEND=0
                BACKEND_NAME="CPU"
            elif [ "$2" = "gpu" ]; then
                BACKEND=3
                BACKEND_NAME="GPU (OpenCL)"
            fi
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 [--mode single|batch|performance] [--batch-size SIZE] [--backend cpu|gpu]"
            exit 1
            ;;
    esac
done

echo "Training Mode: $TRAINING_MODE"
echo "Backend: $BACKEND_NAME"
echo "Batch Sizes: ${BATCH_SIZES[@]}"

# 执行训练
case $TRAINING_MODE in
    "single")
        echo ""
        echo "=== Single Training Run ==="
        BATCH_SIZE=${BATCH_SIZES[0]}
        LOG_FILE="$LOG_DIR/yolov10_single_batch${BATCH_SIZE}_${TIMESTAMP}.log"
        
        echo "Starting YOLOv10 training with batch size $BATCH_SIZE..."
        echo "Backend: $BACKEND_NAME"
        echo "Log file: $LOG_FILE"
        
        adb shell "cd $DEVICE_PATH && export LD_LIBRARY_PATH=$DEVICE_PATH/lib:\$LD_LIBRARY_PATH && ./runTrainDemo.out YOLOv10TrainingDemo data/images data/train.txt $BACKEND $BATCH_SIZE" 2>&1 | tee "$LOG_FILE"
        
        echo "✅ Single training completed. Log saved to: $LOG_FILE"
        ;;
        
    "batch")
        echo ""
        echo "=== Batch Training Test ==="
        LOG_FILE="$LOG_DIR/yolov10_batch_results_${TIMESTAMP}.log"
        
        echo "Starting batch training test..." | tee "$LOG_FILE"
        echo "Device: $DEVICE_MODEL" | tee -a "$LOG_FILE"
        echo "Backend: $BACKEND_NAME" | tee -a "$LOG_FILE"
        echo "Batch Sizes: ${BATCH_SIZES[@]}" | tee -a "$LOG_FILE"
        echo "Timestamp: $TIMESTAMP" | tee -a "$LOG_FILE"
        echo "" | tee -a "$LOG_FILE"
        
        for BATCH_SIZE in "${BATCH_SIZES[@]}"; do
            echo "--- Testing Batch Size: $BATCH_SIZE ---" | tee -a "$LOG_FILE"
            echo "Start time: $(date)" | tee -a "$LOG_FILE"
            
            adb shell "cd $DEVICE_PATH && export LD_LIBRARY_PATH=$DEVICE_PATH/lib:\$LD_LIBRARY_PATH && ./runTrainDemo.out YOLOv10TrainingDemo data/images data/train.txt $BACKEND $BATCH_SIZE" 2>&1 | tee -a "$LOG_FILE"
            
            echo "End time: $(date)" | tee -a "$LOG_FILE"
            echo "Batch size $BATCH_SIZE completed" | tee -a "$LOG_FILE"
            echo "" | tee -a "$LOG_FILE"
            
            # 短暂休息避免设备过热
            sleep 3
        done
        
        echo "✅ Batch training completed. Log saved to: $LOG_FILE"
        ;;
        
    "performance")
        echo ""
        echo "=== Performance Benchmark ==="
        LOG_FILE="$LOG_DIR/yolov10_performance_${TIMESTAMP}.log"
        
        echo "Starting YOLOv10 performance benchmark..." | tee "$LOG_FILE"
        echo "Device: $DEVICE_MODEL" | tee -a "$LOG_FILE"
        echo "Backend: $BACKEND_NAME" | tee -a "$LOG_FILE"
        echo "Test: Performance analysis across different batch sizes" | tee -a "$LOG_FILE"
        echo "Timestamp: $TIMESTAMP" | tee -a "$LOG_FILE"
        echo "" | tee -a "$LOG_FILE"
        
        # 性能测试 - 每个batch size运行多次取平均
        for BATCH_SIZE in "${BATCH_SIZES[@]}"; do
            echo "=== Performance Test: Batch Size $BATCH_SIZE ===" | tee -a "$LOG_FILE"
            
            for RUN in {1..3}; do
                echo "Run $RUN/3:" | tee -a "$LOG_FILE"
                echo "Start time: $(date)" | tee -a "$LOG_FILE"
                
                adb shell "cd $DEVICE_PATH && export LD_LIBRARY_PATH=$DEVICE_PATH/lib:\$LD_LIBRARY_PATH && ./runTrainDemo.out YOLOv10TrainingDemo data/images data/train.txt $BACKEND $BATCH_SIZE" 2>&1 | tee -a "$LOG_FILE"
                
                echo "End time: $(date)" | tee -a "$LOG_FILE"
                echo "" | tee -a "$LOG_FILE"
                
                # 休息避免过热
                sleep 5
            done
            
            echo "Batch size $BATCH_SIZE performance test completed" | tee -a "$LOG_FILE"
            echo "" | tee -a "$LOG_FILE"
        done
        
        echo "✅ Performance benchmark completed. Log saved to: $LOG_FILE"
        ;;
        
    *)
        echo "❌ Error: Unknown training mode: $TRAINING_MODE"
        echo "Supported modes: single, batch, performance"
        exit 1
        ;;
esac

# 收集训练结果
echo ""
echo "=== Collecting Training Results ==="

# 检查设备上是否有保存的模型
if adb shell "ls $DEVICE_PATH/yolov10_trained_*.mnn" 2>/dev/null; then
    echo "✅ Trained models found on device:"
    adb shell "ls -la $DEVICE_PATH/yolov10_trained_*.mnn"
    
    # 可选：拉取训练好的模型到本地
    read -p "Do you want to pull trained models to local? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        mkdir -p "./trained_models"
        adb pull "$DEVICE_PATH/yolov10_trained_*.mnn" "./trained_models/"
        echo "✅ Trained models pulled to ./trained_models/"
    fi
else
    echo "ℹ️  No trained models found on device"
fi

echo ""
echo "=== Training Summary ==="
echo "✅ YOLOv10 mobile training completed successfully!"
echo ""
echo "Training Details:"
echo "  📱 Device: $DEVICE_MODEL"
echo "  🔧 Backend: $BACKEND_NAME"
echo "  📊 Mode: $TRAINING_MODE"
echo "  📦 Batch Sizes: ${BATCH_SIZES[@]}"
echo "  📝 Log File: $LOG_FILE"
echo ""
echo "Next Steps:"
echo "  1. Review training logs in $LOG_DIR/"
echo "  2. Analyze performance metrics"
echo "  3. Test trained models if available"
echo ""
echo "Training execution completed! 🎉"
