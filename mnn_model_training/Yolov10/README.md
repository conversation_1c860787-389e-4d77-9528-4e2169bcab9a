# YOLOv10 Mobile Training with MNN

本目录包含了在手机GPU上使用MNN训练YOLOv10目标检测模型的完整实现。

## 概述

YOLOv10是最新的实时目标检测模型，具有以下特点：
- **NMS-Free**: 消除了非最大抑制，提高推理效率
- **端到端**: 直接输出最终检测结果
- **高效架构**: 优化的backbone和neck结构
- **多尺度检测**: 支持不同尺寸目标的检测

本实现基于EfficientNet训练代码改编，专门针对移动端GPU训练进行了优化。

## 文件结构

```
Yolov10/
├── YOLOv10Model.hpp              # YOLOv10模型结构定义
├── YOLOv10TrainingDemo.cpp       # 训练代码实现
├── YOLOv10DataLoader.hpp         # 数据加载器
├── create_yolov10_test_data.py   # 测试数据生成脚本
├── 0_generate_training_model.sh  # 训练模型生成脚本
├── 1_build_yolov10_android.sh    # Android编译脚本
├── 2_deploy_yolov10_android.sh   # Android部署脚本
├── 3_yolov10_training.sh         # 训练执行脚本
└── README.md                     # 本文档
```

## 快速开始

### 1. 生成测试数据

```bash
# 生成YOLOv10目标检测训练数据
python3 create_yolov10_test_data.py --iterations 20 --max-batch-size 64

# 这将创建：
# - yolov10_test_data/images/     # 训练图像
# - yolov10_test_data/labels/     # YOLO格式标注
# - yolov10_test_data/train.txt   # 训练列表
```

### 2. 生成训练模型（可选）

```bash
# 如果有预训练的YOLOv10推理模型，可以转换为训练模型
./0_generate_training_model.sh
```

### 3. 编译Android训练程序

```bash
# 编译YOLOv10训练程序
./1_build_yolov10_android.sh
```

### 4. 部署到Android设备

```bash
# 部署到连接的Android设备
./2_deploy_yolov10_android.sh
```

### 5. 开始训练

```bash
# 单次训练
./3_yolov10_training.sh --mode single --batch-size 1

# 批量测试不同batch size
./3_yolov10_training.sh --mode batch

# 性能基准测试
./3_yolov10_training.sh --mode performance
```

## 模型架构

### YOLOv10Model.hpp

简化版YOLOv10架构，包含：

1. **Backbone**: 简化版CSPNet
   - Stem: 3→32 channels
   - Stage1: 32→64 channels  
   - Stage2: 64→128 channels
   - Stage3: 128→256 channels
   - Stage4: 256→512 channels

2. **Neck**: 简化版PAN (Path Aggregation Network)
   - 上采样路径：特征融合
   - 下采样路径：多尺度特征

3. **Head**: 单检测头
   - 输出: (4 bbox + 1 objectness + num_classes)

### 关键特性

- **FP32精度**: 确保训练稳定性
- **GPU优化**: 使用NC4HW4格式
- **内存高效**: 简化架构减少内存使用
- **移动端友好**: 针对手机GPU优化

## 训练配置

### 支持的Batch Size
- 1, 4, 8, 16, 32, 64

### 训练参数
- **输入尺寸**: 320×320×3
- **学习率**: 0.0001 (恒定)
- **优化器**: ADAM
- **损失函数**: 简化版检测损失 (MSE-based)
- **Epoch数**: 2 (测试用)

### 数据预处理
- **颜色格式**: RGB
- **归一化**: [0,255] → [0,1]
- **数据增强**: 可选随机裁剪

## 性能优化

### GPU训练优化
1. **精度设置**: FP32高精度
2. **功耗模式**: 普通模式避免过热
3. **内存管理**: 及时垃圾回收
4. **格式转换**: 自动NCHW↔NC4HW4转换

### 移动端适配
1. **热节流管理**: 训练间隔休息
2. **内存优化**: 批处理大小控制
3. **线程配置**: 单线程避免竞争
4. **库依赖**: 最小化依赖

## 使用示例

### 基本训练

```bash
# 在设备上直接运行
adb shell
cd /data/local/tmp/yolov10_training
./run_yolov10_training.sh
```

### 批量性能测试

```bash
# 测试多个batch size的性能
./batch_test_yolov10.sh
```

### 自定义训练

```cpp
// 创建YOLOv10模型
std::shared_ptr<Module> model(new YOLOv10Model(5, 320)); // 5类，320×320输入

// 配置训练器
YOLOv10GPUTrainer::train(
    MNN_FORWARD_OPENCL,  // GPU后端
    1,                   // 线程数
    model,               // 模型
    5,                   // 类别数
    0,                   // 标签偏移
    "data/images",       // 图像目录
    "data/train.txt",    // 训练列表
    "data/images",       // 测试图像目录
    "data/train.txt",    // 测试列表
    320,                 // 输入尺寸
    4                    // batch size
);
```

## 故障排除

### 常见问题

1. **编译失败**
   - 检查Android NDK路径
   - 确保MNN已正确编译

2. **部署失败**
   - 检查ADB连接
   - 确认设备USB调试已开启

3. **训练失败**
   - 检查测试数据是否存在
   - 确认GPU支持OpenCL

4. **性能问题**
   - 降低batch size
   - 检查设备温度
   - 使用CPU后端测试

### 调试技巧

1. **查看日志**
   ```bash
   # 查看训练日志
   cat training_logs/yolov10_*.log
   ```

2. **检查设备状态**
   ```bash
   # 检查GPU支持
   adb shell ls /system/lib64/libOpenCL.so
   
   # 检查设备温度
   adb shell cat /sys/class/thermal/thermal_zone*/temp
   ```

3. **内存监控**
   ```bash
   # 监控内存使用
   adb shell cat /proc/meminfo
   ```

## 扩展开发

### 添加新的损失函数

在`YOLOv10TrainingDemo.cpp`中修改损失计算：

```cpp
// 替换简化的MSE损失为真实的YOLO损失
auto loss = computeYOLOLoss(predict, target, anchors);
```

### 支持更多数据格式

在`YOLOv10DataLoader.hpp`中添加新的配置：

```cpp
static std::shared_ptr<ImageDataset::ImageConfig> createCustomConfig() {
    // 自定义数据预处理配置
}
```

### 模型结构调整

在`YOLOv10Model.hpp`中修改网络结构：

```cpp
// 添加更多层或修改通道数
auto newStagePair = createCSPBlock(in_channels, out_channels, kernel_size, stride);
```

## 参考资料

- [YOLOv10 Paper](https://arxiv.org/abs/2405.14458)
- [Ultralytics YOLOv10](https://docs.ultralytics.com/models/yolov10/)
- [MNN Training Guide](https://www.yuque.com/mnn/cn/train_overview)
- [EfficientNet Training Example](../EfficientNet/)

## 许可证

本项目遵循MNN项目的许可证条款。
