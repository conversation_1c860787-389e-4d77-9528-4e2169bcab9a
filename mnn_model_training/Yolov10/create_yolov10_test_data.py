#!/usr/bin/env python3
"""
YOLOv10 Test Data Generation Script
生成用于YOLOv10目标检测训练的测试图像数据
基于create_real_test_images.py改编，适用于目标检测任务
"""

import os
import sys
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import random
import json

def create_detection_image(width, height, class_id, image_id, num_objects=3):
    """
    创建一个包含目标检测标注的测试图像
    
    Args:
        width: 图像宽度
        height: 图像高度  
        class_id: 主要类别ID
        image_id: 图像ID
        num_objects: 图像中目标的数量
    
    Returns:
        PIL Image对象和边界框标注列表
    """
    # 为不同类别创建不同的颜色模式
    colors = [
        (255, 100, 100),  # 红色系 - 类别0
        (100, 255, 100),  # 绿色系 - 类别1
        (100, 100, 255),  # 蓝色系 - 类别2
        (255, 255, 100),  # 黄色系 - 类别3
        (255, 100, 255),  # 紫色系 - 类别4
    ]
    
    # 创建背景图像
    bg_color = (50, 50, 50)  # 深灰色背景
    image = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(image)
    
    # 存储边界框标注 (YOLO格式: class_id, x_center, y_center, width, height)
    annotations = []
    
    # 在图像中随机放置目标
    for obj_idx in range(num_objects):
        # 随机选择类别 (主要使用指定类别，偶尔使用其他类别)
        if obj_idx == 0 or random.random() < 0.7:
            obj_class = class_id
        else:
            obj_class = random.randint(0, len(colors) - 1)
        
        # 随机生成边界框
        obj_width = random.randint(30, min(width//3, height//3))
        obj_height = random.randint(30, min(width//3, height//3))
        
        # 确保边界框在图像内
        x_min = random.randint(0, width - obj_width)
        y_min = random.randint(0, height - obj_height)
        x_max = x_min + obj_width
        y_max = y_min + obj_height
        
        # 绘制目标 (矩形)
        obj_color = colors[obj_class % len(colors)]
        draw.rectangle([x_min, y_min, x_max, y_max], fill=obj_color, outline=(255, 255, 255), width=2)
        
        # 添加类别标识文本
        try:
            font = ImageFont.load_default()
            text = f"C{obj_class}"
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            # 在边界框内绘制文本
            text_x = x_min + 5
            text_y = y_min + 5
            draw.rectangle([text_x-2, text_y-2, text_x+text_width+2, text_y+text_height+2], 
                          fill=(0, 0, 0), outline=None)
            draw.text((text_x, text_y), text, fill=(255, 255, 255), font=font)
        except:
            pass
        
        # 转换为YOLO格式标注 (归一化坐标)
        x_center = (x_min + x_max) / 2.0 / width
        y_center = (y_min + y_max) / 2.0 / height
        norm_width = obj_width / width
        norm_height = obj_height / height
        
        annotations.append([obj_class, x_center, y_center, norm_width, norm_height])
    
    # 添加一些背景噪声使图像更真实
    for _ in range(10):
        x = random.randint(0, width-10)
        y = random.randint(0, height-10)
        size = random.randint(3, 8)
        noise_color = tuple(random.randint(30, 80) for _ in range(3))
        draw.ellipse([x, y, x+size, y+size], fill=noise_color)
    
    return image, annotations

def create_yolov10_dataset(target_iterations=50, max_batch_size=64, epochs=2):
    """创建YOLOv10目标检测测试数据集

    Args:
        target_iterations: 目标每个epoch的iteration数量
        max_batch_size: 支持的最大batch size
        epochs: 训练epoch数量
    """

    print("=== YOLOv10 Detection Test Data Generation ===")
    print("Creating realistic test images for YOLOv10 object detection training...")
    print()

    # YOLOv10 配置
    IMAGE_WIDTH = 320
    IMAGE_HEIGHT = 320
    NUM_CLASSES = 5

    # 计算需要的图片数量
    TOTAL_IMAGES_NEEDED = target_iterations * max_batch_size
    IMAGES_PER_CLASS = TOTAL_IMAGES_NEEDED // NUM_CLASSES

    print(f"Configuration:")
    print(f"  Target iterations per epoch: {target_iterations}")
    print(f"  Maximum batch size: {max_batch_size}")
    print(f"  Number of epochs: {epochs}")
    print(f"  Total images generated: {TOTAL_IMAGES_NEEDED}")
    print(f"  Images per class: {IMAGES_PER_CLASS}")
    print(f"  Task: Object Detection")
    print()

    # 验证batch size组合
    supported_batch_sizes = [1, 4, 8, 16, 32, 64]
    print("Training strategy:")
    print(f"  All batch sizes will train for exactly {target_iterations} iterations per epoch")
    print("  Data usage per batch size:")
    for batch_size in supported_batch_sizes:
        if batch_size <= max_batch_size:
            images_used = target_iterations * batch_size
            usage_percent = (images_used / TOTAL_IMAGES_NEEDED) * 100
            print(f"    Batch size {batch_size:2d}: uses {images_used:4d}/{TOTAL_IMAGES_NEEDED} images ({usage_percent:.1f}%)")
    print()
    
    # 创建目录结构
    base_dir = "yolov10_test_data"
    train_dir = os.path.join(base_dir, "images")
    labels_dir = os.path.join(base_dir, "labels")
    
    print(f"Creating directory structure...")
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(labels_dir, exist_ok=True)
    print(f"✅ Created: {train_dir}")
    print(f"✅ Created: {labels_dir}")
    
    # 生成图像和标签文件
    train_list = []
    total_images = 0
    
    print(f"\nGenerating detection test images...")
    print(f"Configuration:")
    print(f"  Image Size: {IMAGE_WIDTH}x{IMAGE_HEIGHT}")
    print(f"  Classes: {NUM_CLASSES}")
    print(f"  Images per class: {IMAGES_PER_CLASS}")
    print(f"  Total images: {NUM_CLASSES * IMAGES_PER_CLASS}")
    print(f"  Objects per image: 1-4")
    print()
    
    for class_id in range(NUM_CLASSES):
        print(f"Generating class {class_id}...")
        
        for img_id in range(IMAGES_PER_CLASS):
            # 创建检测图像和标注
            num_objects = random.randint(1, 4)  # 每张图像1-4个目标
            image, annotations = create_detection_image(IMAGE_WIDTH, IMAGE_HEIGHT, class_id, img_id, num_objects)
            
            # 保存图像
            img_filename = f"class{class_id:02d}_img{img_id:03d}.jpg"
            img_filepath = os.path.join(train_dir, img_filename)
            image.save(img_filepath, "JPEG", quality=95)
            
            # 保存YOLO格式标注
            label_filename = f"class{class_id:02d}_img{img_id:03d}.txt"
            label_filepath = os.path.join(labels_dir, label_filename)
            with open(label_filepath, 'w') as f:
                for ann in annotations:
                    # YOLO格式: class_id x_center y_center width height
                    f.write(f"{ann[0]} {ann[1]:.6f} {ann[2]:.6f} {ann[3]:.6f} {ann[4]:.6f}\n")
            
            # 添加到训练列表 (简化格式，只包含图像路径和主类别)
            train_list.append(f"{img_filename} {class_id}")
            total_images += 1
            
            if img_id % 100 == 0 or img_id < 5:
                print(f"  ✅ Created: {img_filename} (class {class_id}, {len(annotations)} objects)")
    
    # 保存训练列表文件
    train_list_file = os.path.join(base_dir, "train.txt")
    with open(train_list_file, 'w') as f:
        for item in train_list:
            f.write(item + '\n')
    
    print(f"\n✅ Training list saved: {train_list_file}")
    
    # 创建数据集配置文件
    dataset_config = {
        "task": "detection",
        "num_classes": NUM_CLASSES,
        "class_names": [f"class_{i}" for i in range(NUM_CLASSES)],
        "image_size": [IMAGE_WIDTH, IMAGE_HEIGHT],
        "train_images": train_dir,
        "train_labels": labels_dir,
        "train_list": train_list_file
    }
    
    config_file = os.path.join(base_dir, "dataset_config.json")
    with open(config_file, 'w') as f:
        json.dump(dataset_config, f, indent=2)
    
    print(f"✅ Dataset config saved: {config_file}")
    
    # 验证生成的数据
    print(f"\n=== Data Verification ===")
    print(f"Total images generated: {total_images}")
    print(f"Images directory: {train_dir}")
    print(f"Labels directory: {labels_dir}")
    print(f"Training list file: {train_list_file}")
    print(f"Dataset config file: {config_file}")
    
    # 检查文件大小
    total_size = 0
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            filepath = os.path.join(root, file)
            total_size += os.path.getsize(filepath)
    
    print(f"Total dataset size: {total_size / (1024*1024):.2f} MB")
    
    # 显示目录结构
    print(f"\nGenerated directory structure:")
    for root, dirs, files in os.walk(base_dir):
        level = root.replace(base_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files[:3]:  # 只显示前3个文件
            print(f"{subindent}{file}")
        if len(files) > 3:
            print(f"{subindent}... and {len(files)-3} more files")
    
    print(f"\n🎉 YOLOv10 detection test data generation completed successfully!")
    print(f"\nNext steps:")
    print(f"  1. Run: ./1_build_yolov10_android.sh")
    print(f"  2. Run: ./2_deploy_yolov10_android.sh")
    print(f"  3. Run: ./3_yolov10_training.sh")
    
    return True

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Generate test images for YOLOv10 object detection training')
    parser.add_argument('--iterations', type=int, default=20,
                       help='Target iterations per epoch (default: 20)')
    parser.add_argument('--max-batch-size', type=int, default=64,
                       help='Maximum batch size to support (default: 64)')
    parser.add_argument('--epochs', type=int, default=2,
                       help='Number of epochs (default: 2)')

    args = parser.parse_args()

    try:
        success = create_yolov10_dataset(
            target_iterations=args.iterations,
            max_batch_size=args.max_batch_size,
            epochs=args.epochs
        )
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
