#!/usr/bin/env python3
"""
Extract YOLOv10 Model Structure from Ultralytics
输出真实的YOLOv10模型结构到文件中
"""

import sys
import torch
import torch.nn as nn
from ultralytics import YOLO
from pathlib import Path

def print_model_structure(model, output_file):
    """打印模型结构到文件"""
    with open(output_file, 'w') as f:
        # 重定向print输出到文件
        original_stdout = sys.stdout
        sys.stdout = f
        
        print("=" * 80)
        print("YOLOv10 Model Structure Analysis")
        print("=" * 80)
        print()
        
        # 1. 基本模型信息
        print("1. Basic Model Information:")
        print(f"   Model Type: {type(model.model)}")
        print(f"   Model Name: {model.model.__class__.__name__}")
        print()
        
        # 2. 模型配置信息
        if hasattr(model.model, 'yaml'):
            print("2. Model Configuration (YAML):")
            print(f"   YAML: {model.model.yaml}")
            print()
        
        # 3. 模型参数统计
        total_params = sum(p.numel() for p in model.model.parameters())
        trainable_params = sum(p.numel() for p in model.model.parameters() if p.requires_grad)
        print("3. Model Parameters:")
        print(f"   Total parameters: {total_params:,}")
        print(f"   Trainable parameters: {trainable_params:,}")
        print()
        
        # 4. 详细的模型结构
        print("4. Detailed Model Structure:")
        print(model.model)
        print()
        
        # 5. 模型的各个模块
        print("5. Model Modules:")
        for name, module in model.model.named_modules():
            if len(list(module.children())) == 0:  # 只显示叶子模块
                print(f"   {name}: {module}")
        print()
        
        # 6. 模型的层级结构
        print("6. Layer Hierarchy:")
        def print_layer_info(module, prefix="", depth=0):
            if depth > 5:  # 限制深度避免过深
                return
            
            for name, child in module.named_children():
                full_name = f"{prefix}.{name}" if prefix else name
                print(f"{'  ' * depth}{full_name}: {child.__class__.__name__}")
                
                # 如果是卷积层，打印详细信息
                if isinstance(child, (nn.Conv2d, nn.ConvTranspose2d)):
                    print(f"{'  ' * (depth+1)}├─ in_channels: {child.in_channels}")
                    print(f"{'  ' * (depth+1)}├─ out_channels: {child.out_channels}")
                    print(f"{'  ' * (depth+1)}├─ kernel_size: {child.kernel_size}")
                    print(f"{'  ' * (depth+1)}├─ stride: {child.stride}")
                    print(f"{'  ' * (depth+1)}└─ padding: {child.padding}")
                
                # 如果是BatchNorm层，打印特征数
                elif isinstance(child, (nn.BatchNorm2d, nn.BatchNorm1d)):
                    print(f"{'  ' * (depth+1)}└─ num_features: {child.num_features}")
                
                # 递归打印子模块
                if len(list(child.children())) > 0:
                    print_layer_info(child, full_name, depth + 1)
        
        print_layer_info(model.model)
        print()
        
        # 7. 前向传播测试
        print("7. Forward Pass Test:")
        try:
            # 创建测试输入
            test_input = torch.randn(1, 3, 320, 320)
            print(f"   Input shape: {test_input.shape}")
            
            # 前向传播
            with torch.no_grad():
                output = model.model(test_input)
            
            if isinstance(output, (list, tuple)):
                print(f"   Output type: {type(output)} (length: {len(output)})")
                for i, out in enumerate(output):
                    if hasattr(out, 'shape'):
                        print(f"   Output[{i}] shape: {out.shape}")
                    else:
                        print(f"   Output[{i}] type: {type(out)}")
            else:
                print(f"   Output shape: {output.shape}")
                print(f"   Output type: {type(output)}")
            
        except Exception as e:
            print(f"   Forward pass failed: {e}")
        print()
        
        # 8. 模型的backbone, neck, head结构分析
        print("8. Architecture Components Analysis:")
        if hasattr(model.model, 'model'):
            backbone_layers = []
            neck_layers = []
            head_layers = []
            
            for i, layer in enumerate(model.model.model):
                layer_info = f"Layer {i}: {layer.__class__.__name__}"
                if hasattr(layer, 'f'):
                    layer_info += f" (from: {layer.f})"
                
                # 根据层的位置和类型判断属于哪个部分
                if i < 10:  # 通常前10层是backbone
                    backbone_layers.append(layer_info)
                elif i < 20:  # 中间层通常是neck
                    neck_layers.append(layer_info)
                else:  # 后面的层通常是head
                    head_layers.append(layer_info)
            
            print("   Backbone layers:")
            for layer in backbone_layers:
                print(f"     {layer}")
            print()
            
            print("   Neck layers:")
            for layer in neck_layers:
                print(f"     {layer}")
            print()
            
            print("   Head layers:")
            for layer in head_layers:
                print(f"     {layer}")
            print()
        
        # 9. 输出通道分析
        print("9. Output Channel Analysis:")
        try:
            # 分析最后几层的输出通道
            if hasattr(model.model, 'model'):
                for i, layer in enumerate(model.model.model[-5:], len(model.model.model)-5):
                    print(f"   Layer {i}: {layer.__class__.__name__}")
                    if hasattr(layer, 'cv3') and hasattr(layer.cv3, 'conv'):
                        print(f"     Output channels: {layer.cv3.conv.out_channels}")
                    elif hasattr(layer, 'conv') and hasattr(layer.conv, 'out_channels'):
                        print(f"     Output channels: {layer.conv.out_channels}")
        except Exception as e:
            print(f"   Channel analysis failed: {e}")
        
        print()
        print("=" * 80)
        print("YOLOv10 Structure Analysis Complete")
        print("=" * 80)
        
        # 恢复stdout
        sys.stdout = original_stdout

def main():
    try:
        print("Loading YOLOv10 model...")
        
        # 加载YOLOv10模型
        model = YOLO("yolov10m.pt")
        
        print("Extracting model structure...")
        
        # 输出模型结构到文件
        output_file = "yolov10_model_structure.txt"
        print_model_structure(model, output_file)
        
        print(f"✅ Model structure saved to: {output_file}")
        print(f"📊 Model summary:")
        print(f"   - Model type: {type(model.model)}")
        print(f"   - Total parameters: {sum(p.numel() for p in model.model.parameters()):,}")
        
        # 测试前向传播
        print("\n🧪 Testing forward pass...")
        test_input = torch.randn(1, 3, 320, 320)
        with torch.no_grad():
            output = model.model(test_input)
        
        if isinstance(output, (list, tuple)):
            print(f"   Output: {len(output)} tensors")
            for i, out in enumerate(output):
                if hasattr(out, 'shape'):
                    print(f"   Output[{i}]: {out.shape}")
        else:
            print(f"   Output shape: {output.shape}")
        
        print("\n🎉 Structure extraction completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
