#!/bin/bash

# Y<PERSON>Ov10 Android Deployment Script
# 部署YOLOv10训练程序到Android设备

set -e

echo "=== YOLOv10 Android Deployment Script ==="

# 设置路径
BUILD_DIR="../../project/android/build_64"
YOLOV10_DIR="$(pwd)"
DEVICE_PATH="/data/local/tmp/yolov10_training"

echo "Build Directory: $BUILD_DIR"
echo "YOLOv10 Directory: $YOLOV10_DIR"
echo "Device Path: $DEVICE_PATH"

# 检查ADB连接
echo ""
echo "=== Checking Android Device Connection ==="
if ! command -v adb &> /dev/null; then
    echo "❌ Error: ADB not found. Please install Android SDK platform-tools"
    exit 1
fi

# 检查设备连接
DEVICE_COUNT=$(adb devices | grep -v "List of devices" | grep -c "device")
if [ $DEVICE_COUNT -eq 0 ]; then
    echo "❌ Error: No Android device connected"
    echo "Please connect an Android device and enable USB debugging"
    exit 1
elif [ $DEVICE_COUNT -gt 1 ]; then
    echo "⚠️  Warning: Multiple devices connected. Using the first one."
fi

DEVICE_ID=$(adb devices | grep "device" | head -1 | awk '{print $1}')
echo "✅ Connected to device: $DEVICE_ID"

# 检查编译文件
echo ""
echo "=== Checking Build Files ==="
REQUIRED_FILES=(
    "$BUILD_DIR/runTrainDemo.out"
    "$BUILD_DIR/libMNN.so"
    "$BUILD_DIR/libMNN_Express.so"
    "$BUILD_DIR/libMNN_CL.so"
    "$BUILD_DIR/libMNNTrain.so"
    "$BUILD_DIR/libMNNTrainUtils.so"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ Found: $(basename $file)"
    else
        echo "❌ Missing: $file"
        echo "Please run ./1_build_yolov10_android.sh first"
        exit 1
    fi
done

# 检查训练模型
TRAINING_MODEL="yolov10_train.mnn"
if [ -f "$TRAINING_MODEL" ]; then
    echo "✅ Found training model: $TRAINING_MODEL"
else
    echo "⚠️  Warning: Training model not found: $TRAINING_MODEL"
    echo "Please run ./0_generate_training_model.sh first"
    echo "Continuing deployment without training model..."
fi

# 检查测试数据
TEST_DATA_DIR="yolov10_test_data"
if [ -d "$TEST_DATA_DIR" ]; then
    echo "✅ Found test data directory: $TEST_DATA_DIR"
else
    echo "⚠️  Warning: Test data not found: $TEST_DATA_DIR"
    echo "Please run: python3 create_yolov10_test_data.py"
    echo "Continuing deployment without test data..."
fi

# 创建设备目录
echo ""
echo "=== Creating Device Directories ==="
adb shell "mkdir -p $DEVICE_PATH"
adb shell "mkdir -p $DEVICE_PATH/lib"
adb shell "mkdir -p $DEVICE_PATH/data"
echo "✅ Device directories created"

# 部署可执行文件
echo ""
echo "=== Deploying Executable ==="
adb push "$BUILD_DIR/runTrainDemo.out" "$DEVICE_PATH/"
adb shell "chmod +x $DEVICE_PATH/runTrainDemo.out"
echo "✅ Executable deployed and made executable"

# 部署库文件
echo ""
echo "=== Deploying Libraries ==="
for file in "${REQUIRED_FILES[@]}"; do
    if [[ "$file" == *.so ]]; then
        echo "Pushing $(basename $file)..."
        adb push "$file" "$DEVICE_PATH/lib/"
    fi
done
echo "✅ All libraries deployed"

# 部署训练模型（如果存在）
if [ -f "$TRAINING_MODEL" ]; then
    echo ""
    echo "=== Deploying Training Model ==="
    adb push "$TRAINING_MODEL" "$DEVICE_PATH/"
    echo "✅ Training model deployed"
fi

# 部署测试数据（如果存在）
if [ -d "$TEST_DATA_DIR" ]; then
    echo ""
    echo "=== Deploying Test Data ==="
    adb push "$TEST_DATA_DIR/images" "$DEVICE_PATH/data/"
    adb push "$TEST_DATA_DIR/train.txt" "$DEVICE_PATH/data/"
    echo "✅ Test data deployed"
fi

# 设置库路径
echo ""
echo "=== Setting Up Environment ==="
adb shell "echo 'export LD_LIBRARY_PATH=$DEVICE_PATH/lib:\$LD_LIBRARY_PATH' > $DEVICE_PATH/setup_env.sh"
adb shell "chmod +x $DEVICE_PATH/setup_env.sh"
echo "✅ Environment setup script created"

# 创建运行脚本
echo ""
echo "=== Creating Run Scripts ==="

# 基本运行脚本
adb shell "cat > $DEVICE_PATH/run_yolov10_training.sh << 'EOF'
#!/system/bin/sh
cd $DEVICE_PATH
export LD_LIBRARY_PATH=$DEVICE_PATH/lib:\$LD_LIBRARY_PATH

echo \"=== YOLOv10 Mobile Training Started ===\"
echo \"Device: \$(getprop ro.product.model)\"
echo \"Android Version: \$(getprop ro.build.version.release)\"
echo \"CPU: \$(getprop ro.product.cpu.abi)\"
echo \"\"

# 检查GPU支持
if [ -f /system/lib64/libOpenCL.so ] || [ -f /vendor/lib64/libOpenCL.so ]; then
    echo \"✅ OpenCL GPU support detected\"
    BACKEND=3
else
    echo \"⚠️  No OpenCL support, using CPU\"
    BACKEND=0
fi

# 运行YOLOv10训练
if [ -d \"data/images\" ] && [ -f \"data/train.txt\" ]; then
    echo \"Starting YOLOv10 object detection training...\"
    ./runTrainDemo.out YOLOv10TrainingDemo data/images data/train.txt \$BACKEND 1
else
    echo \"❌ Error: Training data not found\"
    echo \"Please ensure data/images and data/train.txt exist\"
    exit 1
fi
EOF"

adb shell "chmod +x $DEVICE_PATH/run_yolov10_training.sh"

# 批量测试脚本
adb shell "cat > $DEVICE_PATH/batch_test_yolov10.sh << 'EOF'
#!/system/bin/sh
cd $DEVICE_PATH
export LD_LIBRARY_PATH=$DEVICE_PATH/lib:\$LD_LIBRARY_PATH

echo \"=== YOLOv10 Batch Performance Test ===\"
BATCH_SIZES=\"1 4 8 16\"

for BATCH_SIZE in \$BATCH_SIZES; do
    echo \"\"
    echo \"--- Testing Batch Size: \$BATCH_SIZE ---\"
    if [ -d \"data/images\" ] && [ -f \"data/train.txt\" ]; then
        ./runTrainDemo.out YOLOv10TrainingDemo data/images data/train.txt 3 \$BATCH_SIZE
    else
        echo \"❌ Error: Training data not found\"
        break
    fi
    echo \"Batch size \$BATCH_SIZE completed\"
    sleep 2
done

echo \"\"
echo \"=== YOLOv10 Batch Test Completed ===\"
EOF"

adb shell "chmod +x $DEVICE_PATH/batch_test_yolov10.sh"

echo "✅ Run scripts created"

# 验证部署
echo ""
echo "=== Verifying Deployment ==="
echo "Checking deployed files on device..."

adb shell "ls -la $DEVICE_PATH/"
echo ""
adb shell "ls -la $DEVICE_PATH/lib/"

if [ -d "$TEST_DATA_DIR" ]; then
    echo ""
    echo "Test data:"
    adb shell "ls -la $DEVICE_PATH/data/ | head -10"
fi

echo ""
echo "=== Deployment Summary ==="
echo "✅ YOLOv10 training program deployed successfully!"
echo ""
echo "Deployed files:"
echo "  📱 Device path: $DEVICE_PATH"
echo "  🚀 Executable: runTrainDemo.out"
echo "  📚 Libraries: libMNN*.so files"
if [ -f "$TRAINING_MODEL" ]; then
    echo "  🧠 Training model: $TRAINING_MODEL"
fi
if [ -d "$TEST_DATA_DIR" ]; then
    echo "  📊 Test data: images and labels"
fi
echo ""
echo "Available run scripts:"
echo "  🏃 $DEVICE_PATH/run_yolov10_training.sh - Single training run"
echo "  📊 $DEVICE_PATH/batch_test_yolov10.sh - Batch size performance test"
echo ""
echo "To start training:"
echo "  adb shell"
echo "  cd $DEVICE_PATH"
echo "  ./run_yolov10_training.sh"
echo ""
echo "Or run batch test:"
echo "  ./batch_test_yolov10.sh"
echo ""
echo "Deployment completed successfully! 🎉"
