================================================================================
YOLOv10 Model Structure Analysis
================================================================================

1. Basic Model Information:
   Model Type: <class 'ultralytics.nn.tasks.DetectionModel'>
   Model Name: DetectionModel

2. Model Configuration (YAML):
   YAML: {'nc': 80, 'end2end': True, 'scales': {'m': [0.67, 0.75, 768]}, 'backbone': [[-1, 1, 'Conv', [64, 3, 2]], [-1, 1, 'Conv', [128, 3, 2]], [-1, 3, 'C2f', [128, True]], [-1, 1, 'Conv', [256, 3, 2]], [-1, 6, 'C2f', [256, True]], [-1, 1, 'SCDown', [512, 3, 2]], [-1, 6, 'C2f', [512, True]], [-1, 1, 'SCDown', [1024, 3, 2]], [-1, 3, 'C2fCIB', [1024, True]], [-1, 1, 'SPPF', [1024, 5]], [-1, 1, 'PSA', [1024]]], 'head': [[-1, 1, 'nn.Upsample', ['None', 2, 'nearest']], [[-1, 6], 1, 'Concat', [1]], [-1, 3, 'C2f', [512]], [-1, 1, 'nn.Upsample', ['None', 2, 'nearest']], [[-1, 4], 1, 'Concat', [1]], [-1, 3, 'C2f', [256]], [-1, 1, 'Conv', [256, 3, 2]], [[-1, 13], 1, 'Concat', [1]], [-1, 3, 'C2fCIB', [512, True]], [-1, 1, 'SCDown', [512, 3, 2]], [[-1, 10], 1, 'Concat', [1]], [-1, 3, 'C2fCIB', [1024, True]], [[16, 19, 22], 1, 'v10Detect', ['nc']]], 'scale': 'm', 'yaml_file': 'yolov10m.yaml', 'ch': 3}

3. Model Parameters:
   Total parameters: 16,576,768
   Trainable parameters: 0

4. Detailed Model Structure:
DetectionModel(
  (model): Sequential(
    (0): Conv(
      (conv): Conv2d(3, 48, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
      (bn): BatchNorm2d(48, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
      (act): SiLU(inplace=True)
    )
    (1): Conv(
      (conv): Conv2d(48, 96, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
      (bn): BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
      (act): SiLU(inplace=True)
    )
    (2): C2f(
      (cv1): Conv(
        (conv): Conv2d(96, 96, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(192, 96, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (m): ModuleList(
        (0-1): 2 x Bottleneck(
          (cv1): Conv(
            (conv): Conv2d(48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(48, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (cv2): Conv(
            (conv): Conv2d(48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(48, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
        )
      )
    )
    (3): Conv(
      (conv): Conv2d(96, 192, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
      (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
      (act): SiLU(inplace=True)
    )
    (4): C2f(
      (cv1): Conv(
        (conv): Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(576, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (m): ModuleList(
        (0-3): 4 x Bottleneck(
          (cv1): Conv(
            (conv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (cv2): Conv(
            (conv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
        )
      )
    )
    (5): SCDown(
      (cv1): Conv(
        (conv): Conv2d(192, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), groups=384, bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): Identity()
      )
    )
    (6): C2f(
      (cv1): Conv(
        (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(1152, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (m): ModuleList(
        (0-3): 4 x Bottleneck(
          (cv1): Conv(
            (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (cv2): Conv(
            (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
        )
      )
    )
    (7): SCDown(
      (cv1): Conv(
        (conv): Conv2d(384, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(576, 576, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), groups=576, bias=False)
        (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): Identity()
      )
    )
    (8): C2fCIB(
      (cv1): Conv(
        (conv): Conv2d(576, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(1152, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (m): ModuleList(
        (0-1): 2 x CIB(
          (cv1): Sequential(
            (0): Conv(
              (conv): Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
              (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (2): Conv(
              (conv): Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
              (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (3): Conv(
              (conv): Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (4): Conv(
              (conv): Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
              (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
        )
      )
    )
    (9): SPPF(
      (cv1): Conv(
        (conv): Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(1152, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (m): MaxPool2d(kernel_size=5, stride=1, padding=2, dilation=1, ceil_mode=False)
    )
    (10): PSA(
      (cv1): Conv(
        (conv): Conv2d(576, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(576, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (attn): Attention(
        (qkv): Conv(
          (conv): Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
          (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
          (act): Identity()
        )
        (proj): Conv(
          (conv): Conv2d(288, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
          (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
          (act): Identity()
        )
        (pe): Conv(
          (conv): Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
          (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
          (act): Identity()
        )
      )
      (ffn): Sequential(
        (0): Conv(
          (conv): Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
          (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
          (act): SiLU(inplace=True)
        )
        (1): Conv(
          (conv): Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
          (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
          (act): Identity()
        )
      )
    )
    (11): Upsample(scale_factor=2.0, mode='nearest')
    (12): Concat()
    (13): C2f(
      (cv1): Conv(
        (conv): Conv2d(960, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(768, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (m): ModuleList(
        (0-1): 2 x Bottleneck(
          (cv1): Conv(
            (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (cv2): Conv(
            (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
        )
      )
    )
    (14): Upsample(scale_factor=2.0, mode='nearest')
    (15): Concat()
    (16): C2f(
      (cv1): Conv(
        (conv): Conv2d(576, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(384, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (m): ModuleList(
        (0-1): 2 x Bottleneck(
          (cv1): Conv(
            (conv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (cv2): Conv(
            (conv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
        )
      )
    )
    (17): Conv(
      (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
      (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
      (act): SiLU(inplace=True)
    )
    (18): Concat()
    (19): C2fCIB(
      (cv1): Conv(
        (conv): Conv2d(576, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(768, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (m): ModuleList(
        (0-1): 2 x CIB(
          (cv1): Sequential(
            (0): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(192, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (2): Conv(
              (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384, bias=False)
              (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (3): Conv(
              (conv): Conv2d(384, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (4): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
        )
      )
    )
    (20): SCDown(
      (cv1): Conv(
        (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), groups=384, bias=False)
        (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): Identity()
      )
    )
    (21): Concat()
    (22): C2fCIB(
      (cv1): Conv(
        (conv): Conv2d(960, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (cv2): Conv(
        (conv): Conv2d(1152, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
        (act): SiLU(inplace=True)
      )
      (m): ModuleList(
        (0-1): 2 x CIB(
          (cv1): Sequential(
            (0): Conv(
              (conv): Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
              (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (2): Conv(
              (conv): Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
              (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (3): Conv(
              (conv): Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (4): Conv(
              (conv): Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
              (bn): BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
        )
      )
    )
    (23): v10Detect(
      (cv2): ModuleList(
        (0): Sequential(
          (0): Conv(
            (conv): Conv2d(192, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (1): Conv(
            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (2): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (0): Conv(
            (conv): Conv2d(384, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (1): Conv(
            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (2): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (0): Conv(
            (conv): Conv2d(576, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (1): Conv(
            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (2): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
        )
      )
      (cv3): ModuleList(
        (0): Sequential(
          (0): Sequential(
            (0): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (1): Sequential(
            (0): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (2): Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (0): Sequential(
            (0): Conv(
              (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384, bias=False)
              (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(384, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (1): Sequential(
            (0): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (2): Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (0): Sequential(
            (0): Conv(
              (conv): Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
              (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(576, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (1): Sequential(
            (0): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (2): Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
        )
      )
      (dfl): DFL(
        (conv): Conv2d(16, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)
      )
      (one2one_cv2): ModuleList(
        (0): Sequential(
          (0): Conv(
            (conv): Conv2d(192, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (1): Conv(
            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (2): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (0): Conv(
            (conv): Conv2d(384, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (1): Conv(
            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (2): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (0): Conv(
            (conv): Conv2d(576, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (1): Conv(
            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn): BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
            (act): SiLU(inplace=True)
          )
          (2): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
        )
      )
      (one2one_cv3): ModuleList(
        (0): Sequential(
          (0): Sequential(
            (0): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (1): Sequential(
            (0): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (2): Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (0): Sequential(
            (0): Conv(
              (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384, bias=False)
              (bn): BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(384, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (1): Sequential(
            (0): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (2): Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (0): Sequential(
            (0): Conv(
              (conv): Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
              (bn): BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(576, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (1): Sequential(
            (0): Conv(
              (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
            (1): Conv(
              (conv): Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
              (bn): BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
              (act): SiLU(inplace=True)
            )
          )
          (2): Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
)

5. Model Modules:
   model.0.conv: Conv2d(3, 48, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
   model.0.bn: BatchNorm2d(48, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.0.act: SiLU(inplace=True)
   model.1.conv: Conv2d(48, 96, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
   model.1.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.2.cv1.conv: Conv2d(96, 96, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.2.cv1.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.2.cv2.conv: Conv2d(192, 96, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.2.cv2.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.2.m.0.cv1.conv: Conv2d(48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.2.m.0.cv1.bn: BatchNorm2d(48, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.2.m.0.cv2.conv: Conv2d(48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.2.m.0.cv2.bn: BatchNorm2d(48, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.2.m.1.cv1.conv: Conv2d(48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.2.m.1.cv1.bn: BatchNorm2d(48, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.2.m.1.cv2.conv: Conv2d(48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.2.m.1.cv2.bn: BatchNorm2d(48, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.3.conv: Conv2d(96, 192, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
   model.3.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.cv1.conv: Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.4.cv1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.cv2.conv: Conv2d(576, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.4.cv2.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.m.0.cv1.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.4.m.0.cv1.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.m.0.cv2.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.4.m.0.cv2.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.m.1.cv1.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.4.m.1.cv1.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.m.1.cv2.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.4.m.1.cv2.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.m.2.cv1.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.4.m.2.cv1.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.m.2.cv2.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.4.m.2.cv2.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.m.3.cv1.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.4.m.3.cv1.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.4.m.3.cv2.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.4.m.3.cv2.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.5.cv1.conv: Conv2d(192, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.5.cv1.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.5.cv2.conv: Conv2d(384, 384, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), groups=384, bias=False)
   model.5.cv2.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.5.cv2.act: Identity()
   model.6.cv1.conv: Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.6.cv1.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.6.cv2.conv: Conv2d(1152, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.6.cv2.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.6.m.0.cv1.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.6.m.0.cv1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.6.m.0.cv2.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.6.m.0.cv2.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.6.m.1.cv1.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.6.m.1.cv1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.6.m.1.cv2.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.6.m.1.cv2.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.6.m.2.cv1.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.6.m.2.cv1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.6.m.2.cv2.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.6.m.2.cv2.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.6.m.3.cv1.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.6.m.3.cv1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.6.m.3.cv2.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.6.m.3.cv2.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.7.cv1.conv: Conv2d(384, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.7.cv1.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.7.cv2.conv: Conv2d(576, 576, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), groups=576, bias=False)
   model.7.cv2.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.7.cv2.act: Identity()
   model.8.cv1.conv: Conv2d(576, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.8.cv1.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.cv2.conv: Conv2d(1152, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.8.cv2.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.0.cv1.0.conv: Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
   model.8.m.0.cv1.0.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.0.cv1.1.conv: Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.8.m.0.cv1.1.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.0.cv1.2.conv: Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
   model.8.m.0.cv1.2.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.0.cv1.3.conv: Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.8.m.0.cv1.3.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.0.cv1.4.conv: Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
   model.8.m.0.cv1.4.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.1.cv1.0.conv: Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
   model.8.m.1.cv1.0.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.1.cv1.1.conv: Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.8.m.1.cv1.1.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.1.cv1.2.conv: Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
   model.8.m.1.cv1.2.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.1.cv1.3.conv: Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.8.m.1.cv1.3.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.8.m.1.cv1.4.conv: Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
   model.8.m.1.cv1.4.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.9.cv1.conv: Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.9.cv1.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.9.cv2.conv: Conv2d(1152, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.9.cv2.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.9.m: MaxPool2d(kernel_size=5, stride=1, padding=2, dilation=1, ceil_mode=False)
   model.10.cv1.conv: Conv2d(576, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.10.cv1.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.10.cv2.conv: Conv2d(576, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.10.cv2.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.10.attn.qkv.conv: Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.10.attn.qkv.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.10.attn.qkv.act: Identity()
   model.10.attn.proj.conv: Conv2d(288, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.10.attn.proj.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.10.attn.proj.act: Identity()
   model.10.attn.pe.conv: Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
   model.10.attn.pe.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.10.attn.pe.act: Identity()
   model.10.ffn.0.conv: Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.10.ffn.0.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.10.ffn.1.conv: Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.10.ffn.1.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.10.ffn.1.act: Identity()
   model.11: Upsample(scale_factor=2.0, mode='nearest')
   model.12: Concat()
   model.13.cv1.conv: Conv2d(960, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.13.cv1.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.13.cv2.conv: Conv2d(768, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.13.cv2.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.13.m.0.cv1.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.13.m.0.cv1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.13.m.0.cv2.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.13.m.0.cv2.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.13.m.1.cv1.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.13.m.1.cv1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.13.m.1.cv2.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.13.m.1.cv2.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.14: Upsample(scale_factor=2.0, mode='nearest')
   model.15: Concat()
   model.16.cv1.conv: Conv2d(576, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.16.cv1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.16.cv2.conv: Conv2d(384, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.16.cv2.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.16.m.0.cv1.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.16.m.0.cv1.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.16.m.0.cv2.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.16.m.0.cv2.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.16.m.1.cv1.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.16.m.1.cv1.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.16.m.1.cv2.conv: Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.16.m.1.cv2.bn: BatchNorm2d(96, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.17.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
   model.17.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.18: Concat()
   model.19.cv1.conv: Conv2d(576, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.19.cv1.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.cv2.conv: Conv2d(768, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.19.cv2.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.0.cv1.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.19.m.0.cv1.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.0.cv1.1.conv: Conv2d(192, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.19.m.0.cv1.1.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.0.cv1.2.conv: Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384, bias=False)
   model.19.m.0.cv1.2.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.0.cv1.3.conv: Conv2d(384, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.19.m.0.cv1.3.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.0.cv1.4.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.19.m.0.cv1.4.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.1.cv1.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.19.m.1.cv1.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.1.cv1.1.conv: Conv2d(192, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.19.m.1.cv1.1.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.1.cv1.2.conv: Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384, bias=False)
   model.19.m.1.cv1.2.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.1.cv1.3.conv: Conv2d(384, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.19.m.1.cv1.3.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.19.m.1.cv1.4.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.19.m.1.cv1.4.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.20.cv1.conv: Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.20.cv1.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.20.cv2.conv: Conv2d(384, 384, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), groups=384, bias=False)
   model.20.cv2.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.20.cv2.act: Identity()
   model.21: Concat()
   model.22.cv1.conv: Conv2d(960, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.22.cv1.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.cv2.conv: Conv2d(1152, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.22.cv2.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.0.cv1.0.conv: Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
   model.22.m.0.cv1.0.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.0.cv1.1.conv: Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.22.m.0.cv1.1.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.0.cv1.2.conv: Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
   model.22.m.0.cv1.2.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.0.cv1.3.conv: Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.22.m.0.cv1.3.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.0.cv1.4.conv: Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
   model.22.m.0.cv1.4.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.1.cv1.0.conv: Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
   model.22.m.1.cv1.0.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.1.cv1.1.conv: Conv2d(288, 576, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.22.m.1.cv1.1.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.1.cv1.2.conv: Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
   model.22.m.1.cv1.2.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.1.cv1.3.conv: Conv2d(576, 288, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.22.m.1.cv1.3.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.22.m.1.cv1.4.conv: Conv2d(288, 288, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=288, bias=False)
   model.22.m.1.cv1.4.bn: BatchNorm2d(288, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv2.0.0.conv: Conv2d(192, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.cv2.0.0.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv2.0.1.conv: Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.cv2.0.1.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv2.0.2: Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
   model.23.cv2.1.0.conv: Conv2d(384, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.cv2.1.0.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv2.1.1.conv: Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.cv2.1.1.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv2.1.2: Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
   model.23.cv2.2.0.conv: Conv2d(576, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.cv2.2.0.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv2.2.1.conv: Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.cv2.2.1.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv2.2.2: Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
   model.23.cv3.0.0.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.23.cv3.0.0.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.0.0.1.conv: Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.cv3.0.0.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.0.1.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.23.cv3.0.1.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.0.1.1.conv: Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.cv3.0.1.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.0.2: Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
   model.23.cv3.1.0.0.conv: Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384, bias=False)
   model.23.cv3.1.0.0.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.1.0.1.conv: Conv2d(384, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.cv3.1.0.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.1.1.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.23.cv3.1.1.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.1.1.1.conv: Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.cv3.1.1.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.1.2: Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
   model.23.cv3.2.0.0.conv: Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
   model.23.cv3.2.0.0.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.2.0.1.conv: Conv2d(576, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.cv3.2.0.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.2.1.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.23.cv3.2.1.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.2.1.1.conv: Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.cv3.2.1.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.cv3.2.2: Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
   model.23.dfl.conv: Conv2d(16, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.one2one_cv2.0.0.conv: Conv2d(192, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.one2one_cv2.0.0.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv2.0.0.act: SiLU(inplace=True)
   model.23.one2one_cv2.0.1.conv: Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.one2one_cv2.0.1.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv2.0.2: Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
   model.23.one2one_cv2.1.0.conv: Conv2d(384, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.one2one_cv2.1.0.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv2.1.1.conv: Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.one2one_cv2.1.1.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv2.1.2: Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
   model.23.one2one_cv2.2.0.conv: Conv2d(576, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.one2one_cv2.2.0.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv2.2.1.conv: Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
   model.23.one2one_cv2.2.1.bn: BatchNorm2d(64, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv2.2.2: Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))
   model.23.one2one_cv3.0.0.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.23.one2one_cv3.0.0.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.0.0.0.act: SiLU(inplace=True)
   model.23.one2one_cv3.0.0.1.conv: Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.one2one_cv3.0.0.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.0.1.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.23.one2one_cv3.0.1.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.0.1.1.conv: Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.one2one_cv3.0.1.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.0.2: Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
   model.23.one2one_cv3.1.0.0.conv: Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384, bias=False)
   model.23.one2one_cv3.1.0.0.bn: BatchNorm2d(384, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.1.0.1.conv: Conv2d(384, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.one2one_cv3.1.0.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.1.1.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.23.one2one_cv3.1.1.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.1.1.1.conv: Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.one2one_cv3.1.1.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.1.2: Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))
   model.23.one2one_cv3.2.0.0.conv: Conv2d(576, 576, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=576, bias=False)
   model.23.one2one_cv3.2.0.0.bn: BatchNorm2d(576, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.2.0.1.conv: Conv2d(576, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.one2one_cv3.2.0.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.2.1.0.conv: Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192, bias=False)
   model.23.one2one_cv3.2.1.0.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.2.1.1.conv: Conv2d(192, 192, kernel_size=(1, 1), stride=(1, 1), bias=False)
   model.23.one2one_cv3.2.1.1.bn: BatchNorm2d(192, eps=0.001, momentum=0.03, affine=True, track_running_stats=True)
   model.23.one2one_cv3.2.2: Conv2d(192, 80, kernel_size=(1, 1), stride=(1, 1))

6. Layer Hierarchy:
model: Sequential
  model.0: Conv
    model.0.conv: Conv2d
      ├─ in_channels: 3
      ├─ out_channels: 48
      ├─ kernel_size: (3, 3)
      ├─ stride: (2, 2)
      └─ padding: (1, 1)
    model.0.bn: BatchNorm2d
      └─ num_features: 48
    model.0.act: SiLU
  model.1: Conv
    model.1.conv: Conv2d
      ├─ in_channels: 48
      ├─ out_channels: 96
      ├─ kernel_size: (3, 3)
      ├─ stride: (2, 2)
      └─ padding: (1, 1)
    model.1.bn: BatchNorm2d
      └─ num_features: 96
    model.1.act: SiLU
  model.2: C2f
    model.2.cv1: Conv
      model.2.cv1.conv: Conv2d
        ├─ in_channels: 96
        ├─ out_channels: 96
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.2.cv1.bn: BatchNorm2d
        └─ num_features: 96
      model.2.cv1.act: SiLU
    model.2.cv2: Conv
      model.2.cv2.conv: Conv2d
        ├─ in_channels: 192
        ├─ out_channels: 96
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.2.cv2.bn: BatchNorm2d
        └─ num_features: 96
      model.2.cv2.act: SiLU
    model.2.m: ModuleList
      model.2.m.0: Bottleneck
        model.2.m.0.cv1: Conv
          model.2.m.0.cv1.conv: Conv2d
            ├─ in_channels: 48
            ├─ out_channels: 48
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.2.m.0.cv1.bn: BatchNorm2d
            └─ num_features: 48
          model.2.m.0.cv1.act: SiLU
        model.2.m.0.cv2: Conv
          model.2.m.0.cv2.conv: Conv2d
            ├─ in_channels: 48
            ├─ out_channels: 48
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.2.m.0.cv2.bn: BatchNorm2d
            └─ num_features: 48
          model.2.m.0.cv2.act: SiLU
      model.2.m.1: Bottleneck
        model.2.m.1.cv1: Conv
          model.2.m.1.cv1.conv: Conv2d
            ├─ in_channels: 48
            ├─ out_channels: 48
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.2.m.1.cv1.bn: BatchNorm2d
            └─ num_features: 48
          model.2.m.1.cv1.act: SiLU
        model.2.m.1.cv2: Conv
          model.2.m.1.cv2.conv: Conv2d
            ├─ in_channels: 48
            ├─ out_channels: 48
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.2.m.1.cv2.bn: BatchNorm2d
            └─ num_features: 48
          model.2.m.1.cv2.act: SiLU
  model.3: Conv
    model.3.conv: Conv2d
      ├─ in_channels: 96
      ├─ out_channels: 192
      ├─ kernel_size: (3, 3)
      ├─ stride: (2, 2)
      └─ padding: (1, 1)
    model.3.bn: BatchNorm2d
      └─ num_features: 192
    model.3.act: SiLU
  model.4: C2f
    model.4.cv1: Conv
      model.4.cv1.conv: Conv2d
        ├─ in_channels: 192
        ├─ out_channels: 192
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.4.cv1.bn: BatchNorm2d
        └─ num_features: 192
      model.4.cv1.act: SiLU
    model.4.cv2: Conv
      model.4.cv2.conv: Conv2d
        ├─ in_channels: 576
        ├─ out_channels: 192
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.4.cv2.bn: BatchNorm2d
        └─ num_features: 192
      model.4.cv2.act: SiLU
    model.4.m: ModuleList
      model.4.m.0: Bottleneck
        model.4.m.0.cv1: Conv
          model.4.m.0.cv1.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.4.m.0.cv1.bn: BatchNorm2d
            └─ num_features: 96
          model.4.m.0.cv1.act: SiLU
        model.4.m.0.cv2: Conv
          model.4.m.0.cv2.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.4.m.0.cv2.bn: BatchNorm2d
            └─ num_features: 96
          model.4.m.0.cv2.act: SiLU
      model.4.m.1: Bottleneck
        model.4.m.1.cv1: Conv
          model.4.m.1.cv1.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.4.m.1.cv1.bn: BatchNorm2d
            └─ num_features: 96
          model.4.m.1.cv1.act: SiLU
        model.4.m.1.cv2: Conv
          model.4.m.1.cv2.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.4.m.1.cv2.bn: BatchNorm2d
            └─ num_features: 96
          model.4.m.1.cv2.act: SiLU
      model.4.m.2: Bottleneck
        model.4.m.2.cv1: Conv
          model.4.m.2.cv1.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.4.m.2.cv1.bn: BatchNorm2d
            └─ num_features: 96
          model.4.m.2.cv1.act: SiLU
        model.4.m.2.cv2: Conv
          model.4.m.2.cv2.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.4.m.2.cv2.bn: BatchNorm2d
            └─ num_features: 96
          model.4.m.2.cv2.act: SiLU
      model.4.m.3: Bottleneck
        model.4.m.3.cv1: Conv
          model.4.m.3.cv1.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.4.m.3.cv1.bn: BatchNorm2d
            └─ num_features: 96
          model.4.m.3.cv1.act: SiLU
        model.4.m.3.cv2: Conv
          model.4.m.3.cv2.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.4.m.3.cv2.bn: BatchNorm2d
            └─ num_features: 96
          model.4.m.3.cv2.act: SiLU
  model.5: SCDown
    model.5.cv1: Conv
      model.5.cv1.conv: Conv2d
        ├─ in_channels: 192
        ├─ out_channels: 384
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.5.cv1.bn: BatchNorm2d
        └─ num_features: 384
      model.5.cv1.act: SiLU
    model.5.cv2: Conv
      model.5.cv2.conv: Conv2d
        ├─ in_channels: 384
        ├─ out_channels: 384
        ├─ kernel_size: (3, 3)
        ├─ stride: (2, 2)
        └─ padding: (1, 1)
      model.5.cv2.bn: BatchNorm2d
        └─ num_features: 384
      model.5.cv2.act: Identity
  model.6: C2f
    model.6.cv1: Conv
      model.6.cv1.conv: Conv2d
        ├─ in_channels: 384
        ├─ out_channels: 384
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.6.cv1.bn: BatchNorm2d
        └─ num_features: 384
      model.6.cv1.act: SiLU
    model.6.cv2: Conv
      model.6.cv2.conv: Conv2d
        ├─ in_channels: 1152
        ├─ out_channels: 384
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.6.cv2.bn: BatchNorm2d
        └─ num_features: 384
      model.6.cv2.act: SiLU
    model.6.m: ModuleList
      model.6.m.0: Bottleneck
        model.6.m.0.cv1: Conv
          model.6.m.0.cv1.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.6.m.0.cv1.bn: BatchNorm2d
            └─ num_features: 192
          model.6.m.0.cv1.act: SiLU
        model.6.m.0.cv2: Conv
          model.6.m.0.cv2.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.6.m.0.cv2.bn: BatchNorm2d
            └─ num_features: 192
          model.6.m.0.cv2.act: SiLU
      model.6.m.1: Bottleneck
        model.6.m.1.cv1: Conv
          model.6.m.1.cv1.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.6.m.1.cv1.bn: BatchNorm2d
            └─ num_features: 192
          model.6.m.1.cv1.act: SiLU
        model.6.m.1.cv2: Conv
          model.6.m.1.cv2.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.6.m.1.cv2.bn: BatchNorm2d
            └─ num_features: 192
          model.6.m.1.cv2.act: SiLU
      model.6.m.2: Bottleneck
        model.6.m.2.cv1: Conv
          model.6.m.2.cv1.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.6.m.2.cv1.bn: BatchNorm2d
            └─ num_features: 192
          model.6.m.2.cv1.act: SiLU
        model.6.m.2.cv2: Conv
          model.6.m.2.cv2.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.6.m.2.cv2.bn: BatchNorm2d
            └─ num_features: 192
          model.6.m.2.cv2.act: SiLU
      model.6.m.3: Bottleneck
        model.6.m.3.cv1: Conv
          model.6.m.3.cv1.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.6.m.3.cv1.bn: BatchNorm2d
            └─ num_features: 192
          model.6.m.3.cv1.act: SiLU
        model.6.m.3.cv2: Conv
          model.6.m.3.cv2.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.6.m.3.cv2.bn: BatchNorm2d
            └─ num_features: 192
          model.6.m.3.cv2.act: SiLU
  model.7: SCDown
    model.7.cv1: Conv
      model.7.cv1.conv: Conv2d
        ├─ in_channels: 384
        ├─ out_channels: 576
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.7.cv1.bn: BatchNorm2d
        └─ num_features: 576
      model.7.cv1.act: SiLU
    model.7.cv2: Conv
      model.7.cv2.conv: Conv2d
        ├─ in_channels: 576
        ├─ out_channels: 576
        ├─ kernel_size: (3, 3)
        ├─ stride: (2, 2)
        └─ padding: (1, 1)
      model.7.cv2.bn: BatchNorm2d
        └─ num_features: 576
      model.7.cv2.act: Identity
  model.8: C2fCIB
    model.8.cv1: Conv
      model.8.cv1.conv: Conv2d
        ├─ in_channels: 576
        ├─ out_channels: 576
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.8.cv1.bn: BatchNorm2d
        └─ num_features: 576
      model.8.cv1.act: SiLU
    model.8.cv2: Conv
      model.8.cv2.conv: Conv2d
        ├─ in_channels: 1152
        ├─ out_channels: 576
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.8.cv2.bn: BatchNorm2d
        └─ num_features: 576
      model.8.cv2.act: SiLU
    model.8.m: ModuleList
      model.8.m.0: CIB
        model.8.m.0.cv1: Sequential
          model.8.m.0.cv1.0: Conv
          model.8.m.0.cv1.1: Conv
          model.8.m.0.cv1.2: Conv
          model.8.m.0.cv1.3: Conv
          model.8.m.0.cv1.4: Conv
      model.8.m.1: CIB
        model.8.m.1.cv1: Sequential
          model.8.m.1.cv1.0: Conv
          model.8.m.1.cv1.1: Conv
          model.8.m.1.cv1.2: Conv
          model.8.m.1.cv1.3: Conv
          model.8.m.1.cv1.4: Conv
  model.9: SPPF
    model.9.cv1: Conv
      model.9.cv1.conv: Conv2d
        ├─ in_channels: 576
        ├─ out_channels: 288
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.9.cv1.bn: BatchNorm2d
        └─ num_features: 288
      model.9.cv1.act: SiLU
    model.9.cv2: Conv
      model.9.cv2.conv: Conv2d
        ├─ in_channels: 1152
        ├─ out_channels: 576
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.9.cv2.bn: BatchNorm2d
        └─ num_features: 576
      model.9.cv2.act: SiLU
    model.9.m: MaxPool2d
  model.10: PSA
    model.10.cv1: Conv
      model.10.cv1.conv: Conv2d
        ├─ in_channels: 576
        ├─ out_channels: 576
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.10.cv1.bn: BatchNorm2d
        └─ num_features: 576
      model.10.cv1.act: SiLU
    model.10.cv2: Conv
      model.10.cv2.conv: Conv2d
        ├─ in_channels: 576
        ├─ out_channels: 576
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.10.cv2.bn: BatchNorm2d
        └─ num_features: 576
      model.10.cv2.act: SiLU
    model.10.attn: Attention
      model.10.attn.qkv: Conv
        model.10.attn.qkv.conv: Conv2d
          ├─ in_channels: 288
          ├─ out_channels: 576
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
        model.10.attn.qkv.bn: BatchNorm2d
          └─ num_features: 576
        model.10.attn.qkv.act: Identity
      model.10.attn.proj: Conv
        model.10.attn.proj.conv: Conv2d
          ├─ in_channels: 288
          ├─ out_channels: 288
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
        model.10.attn.proj.bn: BatchNorm2d
          └─ num_features: 288
        model.10.attn.proj.act: Identity
      model.10.attn.pe: Conv
        model.10.attn.pe.conv: Conv2d
          ├─ in_channels: 288
          ├─ out_channels: 288
          ├─ kernel_size: (3, 3)
          ├─ stride: (1, 1)
          └─ padding: (1, 1)
        model.10.attn.pe.bn: BatchNorm2d
          └─ num_features: 288
        model.10.attn.pe.act: Identity
    model.10.ffn: Sequential
      model.10.ffn.0: Conv
        model.10.ffn.0.conv: Conv2d
          ├─ in_channels: 288
          ├─ out_channels: 576
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
        model.10.ffn.0.bn: BatchNorm2d
          └─ num_features: 576
        model.10.ffn.0.act: SiLU
      model.10.ffn.1: Conv
        model.10.ffn.1.conv: Conv2d
          ├─ in_channels: 576
          ├─ out_channels: 288
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
        model.10.ffn.1.bn: BatchNorm2d
          └─ num_features: 288
        model.10.ffn.1.act: Identity
  model.11: Upsample
  model.12: Concat
  model.13: C2f
    model.13.cv1: Conv
      model.13.cv1.conv: Conv2d
        ├─ in_channels: 960
        ├─ out_channels: 384
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.13.cv1.bn: BatchNorm2d
        └─ num_features: 384
      model.13.cv1.act: SiLU
    model.13.cv2: Conv
      model.13.cv2.conv: Conv2d
        ├─ in_channels: 768
        ├─ out_channels: 384
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.13.cv2.bn: BatchNorm2d
        └─ num_features: 384
      model.13.cv2.act: SiLU
    model.13.m: ModuleList
      model.13.m.0: Bottleneck
        model.13.m.0.cv1: Conv
          model.13.m.0.cv1.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.13.m.0.cv1.bn: BatchNorm2d
            └─ num_features: 192
          model.13.m.0.cv1.act: SiLU
        model.13.m.0.cv2: Conv
          model.13.m.0.cv2.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.13.m.0.cv2.bn: BatchNorm2d
            └─ num_features: 192
          model.13.m.0.cv2.act: SiLU
      model.13.m.1: Bottleneck
        model.13.m.1.cv1: Conv
          model.13.m.1.cv1.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.13.m.1.cv1.bn: BatchNorm2d
            └─ num_features: 192
          model.13.m.1.cv1.act: SiLU
        model.13.m.1.cv2: Conv
          model.13.m.1.cv2.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 192
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.13.m.1.cv2.bn: BatchNorm2d
            └─ num_features: 192
          model.13.m.1.cv2.act: SiLU
  model.14: Upsample
  model.15: Concat
  model.16: C2f
    model.16.cv1: Conv
      model.16.cv1.conv: Conv2d
        ├─ in_channels: 576
        ├─ out_channels: 192
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.16.cv1.bn: BatchNorm2d
        └─ num_features: 192
      model.16.cv1.act: SiLU
    model.16.cv2: Conv
      model.16.cv2.conv: Conv2d
        ├─ in_channels: 384
        ├─ out_channels: 192
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.16.cv2.bn: BatchNorm2d
        └─ num_features: 192
      model.16.cv2.act: SiLU
    model.16.m: ModuleList
      model.16.m.0: Bottleneck
        model.16.m.0.cv1: Conv
          model.16.m.0.cv1.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.16.m.0.cv1.bn: BatchNorm2d
            └─ num_features: 96
          model.16.m.0.cv1.act: SiLU
        model.16.m.0.cv2: Conv
          model.16.m.0.cv2.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.16.m.0.cv2.bn: BatchNorm2d
            └─ num_features: 96
          model.16.m.0.cv2.act: SiLU
      model.16.m.1: Bottleneck
        model.16.m.1.cv1: Conv
          model.16.m.1.cv1.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.16.m.1.cv1.bn: BatchNorm2d
            └─ num_features: 96
          model.16.m.1.cv1.act: SiLU
        model.16.m.1.cv2: Conv
          model.16.m.1.cv2.conv: Conv2d
            ├─ in_channels: 96
            ├─ out_channels: 96
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.16.m.1.cv2.bn: BatchNorm2d
            └─ num_features: 96
          model.16.m.1.cv2.act: SiLU
  model.17: Conv
    model.17.conv: Conv2d
      ├─ in_channels: 192
      ├─ out_channels: 192
      ├─ kernel_size: (3, 3)
      ├─ stride: (2, 2)
      └─ padding: (1, 1)
    model.17.bn: BatchNorm2d
      └─ num_features: 192
    model.17.act: SiLU
  model.18: Concat
  model.19: C2fCIB
    model.19.cv1: Conv
      model.19.cv1.conv: Conv2d
        ├─ in_channels: 576
        ├─ out_channels: 384
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.19.cv1.bn: BatchNorm2d
        └─ num_features: 384
      model.19.cv1.act: SiLU
    model.19.cv2: Conv
      model.19.cv2.conv: Conv2d
        ├─ in_channels: 768
        ├─ out_channels: 384
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.19.cv2.bn: BatchNorm2d
        └─ num_features: 384
      model.19.cv2.act: SiLU
    model.19.m: ModuleList
      model.19.m.0: CIB
        model.19.m.0.cv1: Sequential
          model.19.m.0.cv1.0: Conv
          model.19.m.0.cv1.1: Conv
          model.19.m.0.cv1.2: Conv
          model.19.m.0.cv1.3: Conv
          model.19.m.0.cv1.4: Conv
      model.19.m.1: CIB
        model.19.m.1.cv1: Sequential
          model.19.m.1.cv1.0: Conv
          model.19.m.1.cv1.1: Conv
          model.19.m.1.cv1.2: Conv
          model.19.m.1.cv1.3: Conv
          model.19.m.1.cv1.4: Conv
  model.20: SCDown
    model.20.cv1: Conv
      model.20.cv1.conv: Conv2d
        ├─ in_channels: 384
        ├─ out_channels: 384
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.20.cv1.bn: BatchNorm2d
        └─ num_features: 384
      model.20.cv1.act: SiLU
    model.20.cv2: Conv
      model.20.cv2.conv: Conv2d
        ├─ in_channels: 384
        ├─ out_channels: 384
        ├─ kernel_size: (3, 3)
        ├─ stride: (2, 2)
        └─ padding: (1, 1)
      model.20.cv2.bn: BatchNorm2d
        └─ num_features: 384
      model.20.cv2.act: Identity
  model.21: Concat
  model.22: C2fCIB
    model.22.cv1: Conv
      model.22.cv1.conv: Conv2d
        ├─ in_channels: 960
        ├─ out_channels: 576
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.22.cv1.bn: BatchNorm2d
        └─ num_features: 576
      model.22.cv1.act: SiLU
    model.22.cv2: Conv
      model.22.cv2.conv: Conv2d
        ├─ in_channels: 1152
        ├─ out_channels: 576
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
      model.22.cv2.bn: BatchNorm2d
        └─ num_features: 576
      model.22.cv2.act: SiLU
    model.22.m: ModuleList
      model.22.m.0: CIB
        model.22.m.0.cv1: Sequential
          model.22.m.0.cv1.0: Conv
          model.22.m.0.cv1.1: Conv
          model.22.m.0.cv1.2: Conv
          model.22.m.0.cv1.3: Conv
          model.22.m.0.cv1.4: Conv
      model.22.m.1: CIB
        model.22.m.1.cv1: Sequential
          model.22.m.1.cv1.0: Conv
          model.22.m.1.cv1.1: Conv
          model.22.m.1.cv1.2: Conv
          model.22.m.1.cv1.3: Conv
          model.22.m.1.cv1.4: Conv
  model.23: v10Detect
    model.23.cv2: ModuleList
      model.23.cv2.0: Sequential
        model.23.cv2.0.0: Conv
          model.23.cv2.0.0.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.cv2.0.0.bn: BatchNorm2d
            └─ num_features: 64
          model.23.cv2.0.0.act: SiLU
        model.23.cv2.0.1: Conv
          model.23.cv2.0.1.conv: Conv2d
            ├─ in_channels: 64
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.cv2.0.1.bn: BatchNorm2d
            └─ num_features: 64
          model.23.cv2.0.1.act: SiLU
        model.23.cv2.0.2: Conv2d
          ├─ in_channels: 64
          ├─ out_channels: 64
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
      model.23.cv2.1: Sequential
        model.23.cv2.1.0: Conv
          model.23.cv2.1.0.conv: Conv2d
            ├─ in_channels: 384
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.cv2.1.0.bn: BatchNorm2d
            └─ num_features: 64
          model.23.cv2.1.0.act: SiLU
        model.23.cv2.1.1: Conv
          model.23.cv2.1.1.conv: Conv2d
            ├─ in_channels: 64
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.cv2.1.1.bn: BatchNorm2d
            └─ num_features: 64
          model.23.cv2.1.1.act: SiLU
        model.23.cv2.1.2: Conv2d
          ├─ in_channels: 64
          ├─ out_channels: 64
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
      model.23.cv2.2: Sequential
        model.23.cv2.2.0: Conv
          model.23.cv2.2.0.conv: Conv2d
            ├─ in_channels: 576
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.cv2.2.0.bn: BatchNorm2d
            └─ num_features: 64
          model.23.cv2.2.0.act: SiLU
        model.23.cv2.2.1: Conv
          model.23.cv2.2.1.conv: Conv2d
            ├─ in_channels: 64
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.cv2.2.1.bn: BatchNorm2d
            └─ num_features: 64
          model.23.cv2.2.1.act: SiLU
        model.23.cv2.2.2: Conv2d
          ├─ in_channels: 64
          ├─ out_channels: 64
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
    model.23.cv3: ModuleList
      model.23.cv3.0: Sequential
        model.23.cv3.0.0: Sequential
          model.23.cv3.0.0.0: Conv
          model.23.cv3.0.0.1: Conv
        model.23.cv3.0.1: Sequential
          model.23.cv3.0.1.0: Conv
          model.23.cv3.0.1.1: Conv
        model.23.cv3.0.2: Conv2d
          ├─ in_channels: 192
          ├─ out_channels: 80
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
      model.23.cv3.1: Sequential
        model.23.cv3.1.0: Sequential
          model.23.cv3.1.0.0: Conv
          model.23.cv3.1.0.1: Conv
        model.23.cv3.1.1: Sequential
          model.23.cv3.1.1.0: Conv
          model.23.cv3.1.1.1: Conv
        model.23.cv3.1.2: Conv2d
          ├─ in_channels: 192
          ├─ out_channels: 80
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
      model.23.cv3.2: Sequential
        model.23.cv3.2.0: Sequential
          model.23.cv3.2.0.0: Conv
          model.23.cv3.2.0.1: Conv
        model.23.cv3.2.1: Sequential
          model.23.cv3.2.1.0: Conv
          model.23.cv3.2.1.1: Conv
        model.23.cv3.2.2: Conv2d
          ├─ in_channels: 192
          ├─ out_channels: 80
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
    model.23.dfl: DFL
      model.23.dfl.conv: Conv2d
        ├─ in_channels: 16
        ├─ out_channels: 1
        ├─ kernel_size: (1, 1)
        ├─ stride: (1, 1)
        └─ padding: (0, 0)
    model.23.one2one_cv2: ModuleList
      model.23.one2one_cv2.0: Sequential
        model.23.one2one_cv2.0.0: Conv
          model.23.one2one_cv2.0.0.conv: Conv2d
            ├─ in_channels: 192
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.one2one_cv2.0.0.bn: BatchNorm2d
            └─ num_features: 64
          model.23.one2one_cv2.0.0.act: SiLU
        model.23.one2one_cv2.0.1: Conv
          model.23.one2one_cv2.0.1.conv: Conv2d
            ├─ in_channels: 64
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.one2one_cv2.0.1.bn: BatchNorm2d
            └─ num_features: 64
          model.23.one2one_cv2.0.1.act: SiLU
        model.23.one2one_cv2.0.2: Conv2d
          ├─ in_channels: 64
          ├─ out_channels: 64
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
      model.23.one2one_cv2.1: Sequential
        model.23.one2one_cv2.1.0: Conv
          model.23.one2one_cv2.1.0.conv: Conv2d
            ├─ in_channels: 384
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.one2one_cv2.1.0.bn: BatchNorm2d
            └─ num_features: 64
          model.23.one2one_cv2.1.0.act: SiLU
        model.23.one2one_cv2.1.1: Conv
          model.23.one2one_cv2.1.1.conv: Conv2d
            ├─ in_channels: 64
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.one2one_cv2.1.1.bn: BatchNorm2d
            └─ num_features: 64
          model.23.one2one_cv2.1.1.act: SiLU
        model.23.one2one_cv2.1.2: Conv2d
          ├─ in_channels: 64
          ├─ out_channels: 64
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
      model.23.one2one_cv2.2: Sequential
        model.23.one2one_cv2.2.0: Conv
          model.23.one2one_cv2.2.0.conv: Conv2d
            ├─ in_channels: 576
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.one2one_cv2.2.0.bn: BatchNorm2d
            └─ num_features: 64
          model.23.one2one_cv2.2.0.act: SiLU
        model.23.one2one_cv2.2.1: Conv
          model.23.one2one_cv2.2.1.conv: Conv2d
            ├─ in_channels: 64
            ├─ out_channels: 64
            ├─ kernel_size: (3, 3)
            ├─ stride: (1, 1)
            └─ padding: (1, 1)
          model.23.one2one_cv2.2.1.bn: BatchNorm2d
            └─ num_features: 64
          model.23.one2one_cv2.2.1.act: SiLU
        model.23.one2one_cv2.2.2: Conv2d
          ├─ in_channels: 64
          ├─ out_channels: 64
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
    model.23.one2one_cv3: ModuleList
      model.23.one2one_cv3.0: Sequential
        model.23.one2one_cv3.0.0: Sequential
          model.23.one2one_cv3.0.0.0: Conv
          model.23.one2one_cv3.0.0.1: Conv
        model.23.one2one_cv3.0.1: Sequential
          model.23.one2one_cv3.0.1.0: Conv
          model.23.one2one_cv3.0.1.1: Conv
        model.23.one2one_cv3.0.2: Conv2d
          ├─ in_channels: 192
          ├─ out_channels: 80
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
      model.23.one2one_cv3.1: Sequential
        model.23.one2one_cv3.1.0: Sequential
          model.23.one2one_cv3.1.0.0: Conv
          model.23.one2one_cv3.1.0.1: Conv
        model.23.one2one_cv3.1.1: Sequential
          model.23.one2one_cv3.1.1.0: Conv
          model.23.one2one_cv3.1.1.1: Conv
        model.23.one2one_cv3.1.2: Conv2d
          ├─ in_channels: 192
          ├─ out_channels: 80
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)
      model.23.one2one_cv3.2: Sequential
        model.23.one2one_cv3.2.0: Sequential
          model.23.one2one_cv3.2.0.0: Conv
          model.23.one2one_cv3.2.0.1: Conv
        model.23.one2one_cv3.2.1: Sequential
          model.23.one2one_cv3.2.1.0: Conv
          model.23.one2one_cv3.2.1.1: Conv
        model.23.one2one_cv3.2.2: Conv2d
          ├─ in_channels: 192
          ├─ out_channels: 80
          ├─ kernel_size: (1, 1)
          ├─ stride: (1, 1)
          └─ padding: (0, 0)

7. Forward Pass Test:
   Input shape: torch.Size([1, 3, 320, 320])
   Output type: <class 'tuple'> (length: 2)
   Output[0] shape: torch.Size([1, 300, 6])
   Output[1] type: <class 'dict'>

8. Architecture Components Analysis:
   Backbone layers:
     Layer 0: Conv (from: -1)
     Layer 1: Conv (from: -1)
     Layer 2: C2f (from: -1)
     Layer 3: Conv (from: -1)
     Layer 4: C2f (from: -1)
     Layer 5: SCDown (from: -1)
     Layer 6: C2f (from: -1)
     Layer 7: SCDown (from: -1)
     Layer 8: C2fCIB (from: -1)
     Layer 9: SPPF (from: -1)

   Neck layers:
     Layer 10: PSA (from: -1)
     Layer 11: Upsample (from: -1)
     Layer 12: Concat (from: [-1, 6])
     Layer 13: C2f (from: -1)
     Layer 14: Upsample (from: -1)
     Layer 15: Concat (from: [-1, 4])
     Layer 16: C2f (from: -1)
     Layer 17: Conv (from: -1)
     Layer 18: Concat (from: [-1, 13])
     Layer 19: C2fCIB (from: -1)

   Head layers:
     Layer 20: SCDown (from: -1)
     Layer 21: Concat (from: [-1, 10])
     Layer 22: C2fCIB (from: -1)
     Layer 23: v10Detect (from: [16, 19, 22])

9. Output Channel Analysis:
   Layer 19: C2fCIB
   Layer 20: SCDown
   Layer 21: Concat
   Layer 22: C2fCIB
   Layer 23: v10Detect

================================================================================
YOLOv10 Structure Analysis Complete
================================================================================
