//
//  YOLOv10Model.hpp
//  MNN
//
//  YOLOv10模型结构 - 基于Ultralytics YOLOv10实现
//  适用于手机GPU训练，使用FP32精度
//

#ifndef YOLOV10_MODEL_HPP
#define YOLOV10_MODEL_HPP

#include <MNN/expr/Module.hpp>
#include "NN.hpp"
#include <memory>
#include <iostream>
#include <vector>

using namespace MNN;
using namespace MNN::Express;
using namespace MNN::Train;
using namespace std;

/**
 * YOLOv10训练模块
 * 基于Ultralytics YOLOv10架构，简化版本适用于移动端训练
 * 
 * 架构详情:
 * - Backbone: 简化版CSPNet (4个stage)
 * - Neck: 简化版PAN (Path Aggregation Network)
 * - Head: 单头结构用于训练 (简化版，不使用dual assignment)
 * 
 * 输入: 320x320x3 (YOLOv10标准输入尺寸)
 * 输出: 用于目标检测的特征图
 */
class YOLOv10Model : public Module {
public:
    /**
     * 构造函数
     * @param num_classes 检测类别数 (默认80，COCO数据集)
     * @param input_size 输入图像尺寸 (默认320)
     */
    explicit YOLOv10Model(int num_classes = 80, int input_size = 320);
    
    /**
     * 前向传播
     * @param inputs 输入张量列表
     * @return 输出张量列表
     */
    virtual std::vector<VARP> onForward(const std::vector<VARP>& inputs) override;
    
    /**
     * 获取模型信息
     */
    void printModelInfo() const;

private:
    int mNumClasses;
    int mInputSize;
    
    // Backbone - 简化版CSPNet
    std::shared_ptr<Module> mStem, mStemBn;
    
    // 4个CSP Stage
    std::shared_ptr<Module> mStage1, mStage1Bn;
    std::shared_ptr<Module> mStage2, mStage2Bn;
    std::shared_ptr<Module> mStage3, mStage3Bn;
    std::shared_ptr<Module> mStage4, mStage4Bn;
    
    // Neck - 简化版PAN
    std::shared_ptr<Module> mNeckUp1, mNeckUp1Bn;
    std::shared_ptr<Module> mNeckUp2, mNeckUp2Bn;
    std::shared_ptr<Module> mNeckDown1, mNeckDown1Bn;
    std::shared_ptr<Module> mNeckDown2, mNeckDown2Bn;
    
    // Head - 检测头
    std::shared_ptr<Module> mDetectHead1, mDetectHead1Bn;
    std::shared_ptr<Module> mDetectHead2, mDetectHead2Bn;
    std::shared_ptr<Module> mDetectHead3, mDetectHead3Bn;
    
    // 最终输出层
    std::shared_ptr<Module> mOutputConv;
    
    /**
     * 初始化所有层
     */
    void initializeLayers();
    
    /**
     * 创建CSP Block
     */
    std::pair<std::shared_ptr<Module>, std::shared_ptr<Module>> 
    createCSPBlock(int in_channels, int out_channels, int kernel_size = 3, int stride = 1);
};

// 实现部分
inline YOLOv10Model::YOLOv10Model(int num_classes, int input_size) 
    : mNumClasses(num_classes), mInputSize(input_size) {
    initializeLayers();
}

inline std::pair<std::shared_ptr<Module>, std::shared_ptr<Module>> 
YOLOv10Model::createCSPBlock(int in_channels, int out_channels, int kernel_size, int stride) {
    NN::ConvOption convOption;
    convOption.kernelSize = {kernel_size, kernel_size};
    convOption.channel = {in_channels, out_channels};
    convOption.padMode = Express::SAME;
    convOption.stride = {stride, stride};
    
    auto conv = std::shared_ptr<Module>(NN::Conv(convOption, false));
    auto bn = std::shared_ptr<Module>(NN::BatchNorm(out_channels));
    
    return std::make_pair(conv, bn);
}

inline void YOLOv10Model::initializeLayers() {
    // Stem: 3x3 conv, stride 2, 3->32
    auto stemPair = createCSPBlock(3, 32, 3, 2);
    mStem = stemPair.first;
    mStemBn = stemPair.second;
    
    // Stage 1: 32->64, stride 2
    auto stage1Pair = createCSPBlock(32, 64, 3, 2);
    mStage1 = stage1Pair.first;
    mStage1Bn = stage1Pair.second;
    
    // Stage 2: 64->128, stride 2
    auto stage2Pair = createCSPBlock(64, 128, 3, 2);
    mStage2 = stage2Pair.first;
    mStage2Bn = stage2Pair.second;
    
    // Stage 3: 128->256, stride 2
    auto stage3Pair = createCSPBlock(128, 256, 3, 2);
    mStage3 = stage3Pair.first;
    mStage3Bn = stage3Pair.second;
    
    // Stage 4: 256->512, stride 2
    auto stage4Pair = createCSPBlock(256, 512, 3, 2);
    mStage4 = stage4Pair.first;
    mStage4Bn = stage4Pair.second;
    
    // Neck - PAN结构
    // 上采样路径
    auto neckUp1Pair = createCSPBlock(512, 256, 1, 1);
    mNeckUp1 = neckUp1Pair.first;
    mNeckUp1Bn = neckUp1Pair.second;
    
    auto neckUp2Pair = createCSPBlock(256, 128, 1, 1);
    mNeckUp2 = neckUp2Pair.first;
    mNeckUp2Bn = neckUp2Pair.second;
    
    // 下采样路径
    auto neckDown1Pair = createCSPBlock(128, 256, 3, 2);
    mNeckDown1 = neckDown1Pair.first;
    mNeckDown1Bn = neckDown1Pair.second;
    
    auto neckDown2Pair = createCSPBlock(256, 512, 3, 2);
    mNeckDown2 = neckDown2Pair.first;
    mNeckDown2Bn = neckDown2Pair.second;
    
    // Detection Head - 3个尺度的检测头
    auto detectHead1Pair = createCSPBlock(128, 128, 3, 1);
    mDetectHead1 = detectHead1Pair.first;
    mDetectHead1Bn = detectHead1Pair.second;
    
    auto detectHead2Pair = createCSPBlock(256, 256, 3, 1);
    mDetectHead2 = detectHead2Pair.first;
    mDetectHead2Bn = detectHead2Pair.second;
    
    auto detectHead3Pair = createCSPBlock(512, 512, 3, 1);
    mDetectHead3 = detectHead3Pair.first;
    mDetectHead3Bn = detectHead3Pair.second;
    
    // 最终输出层 - 输出检测结果
    // 每个anchor预测: (x, y, w, h, objectness, class_probs)
    // 输出通道数 = (4 + 1 + num_classes) = 5 + num_classes
    int output_channels = 5 + mNumClasses;
    NN::ConvOption outputOption;
    outputOption.kernelSize = {1, 1};
    outputOption.channel = {512, output_channels}; // 使用最大特征通道数
    outputOption.padMode = Express::SAME;
    outputOption.stride = {1, 1};
    mOutputConv.reset(NN::Conv(outputOption, true)); // 使用bias
    
    // 注册所有参数
    registerModel({
        mStem, mStemBn,
        mStage1, mStage1Bn, mStage2, mStage2Bn,
        mStage3, mStage3Bn, mStage4, mStage4Bn,
        mNeckUp1, mNeckUp1Bn, mNeckUp2, mNeckUp2Bn,
        mNeckDown1, mNeckDown1Bn, mNeckDown2, mNeckDown2Bn,
        mDetectHead1, mDetectHead1Bn, mDetectHead2, mDetectHead2Bn,
        mDetectHead3, mDetectHead3Bn,
        mOutputConv
    });
}

inline std::vector<VARP> YOLOv10Model::onForward(const std::vector<VARP>& inputs) {
    using namespace Express;

    // 安全检查
    if (inputs.empty() || inputs[0].get() == nullptr) {
        cout << "Error: Empty or null input to YOLOv10Model" << endl;
        return {};
    }

    VARP x = inputs[0];

    // 检查输入维度
    auto inputInfo = x->getInfo();
    if (!inputInfo || inputInfo->dim.size() != 4) {
        cout << "Error: Invalid input dimensions to YOLOv10Model" << endl;
        return {};
    }

    // 确保输入格式正确 (NCHW -> NC4HW4 for GPU)
    x = _Convert(x, NC4HW4);

    // Backbone - 特征提取
    // Stem
    x = mStem->forward(x);
    x = mStemBn->forward(x);
    x = _Relu6(x);

    // Stage 1: 32->64
    x = mStage1->forward(x);
    x = mStage1Bn->forward(x);
    VARP feat1 = _Relu6(x); // 保存用于neck

    // Stage 2: 64->128
    x = mStage2->forward(feat1);
    x = mStage2Bn->forward(x);
    VARP feat2 = _Relu6(x); // 保存用于neck

    // Stage 3: 128->256
    x = mStage3->forward(feat2);
    x = mStage3Bn->forward(x);
    VARP feat3 = _Relu6(x); // 保存用于neck

    // Stage 4: 256->512
    x = mStage4->forward(feat3);
    x = mStage4Bn->forward(x);
    VARP feat4 = _Relu6(x); // 最深层特征

    // Neck - PAN特征融合
    // 上采样路径
    x = mNeckUp1->forward(feat4);
    x = mNeckUp1Bn->forward(x);
    x = _Relu6(x);

    // 上采样并与feat3融合
    x = _Resize(x, 2.0f, 2.0f); // 上采样2倍
    x = _Add(x, feat3); // 特征融合

    x = mNeckUp2->forward(x);
    x = mNeckUp2Bn->forward(x);
    VARP neck_feat1 = _Relu6(x); // 第一个neck输出

    // 下采样路径
    x = mNeckDown1->forward(neck_feat1);
    x = mNeckDown1Bn->forward(x);
    x = _Relu6(x);
    x = _Add(x, feat3); // 与feat3融合
    VARP neck_feat2 = x; // 第二个neck输出

    x = mNeckDown2->forward(neck_feat2);
    x = mNeckDown2Bn->forward(x);
    x = _Relu6(x);
    x = _Add(x, feat4); // 与feat4融合
    VARP neck_feat3 = x; // 第三个neck输出

    // Detection Head - 多尺度检测
    // 小目标检测 (高分辨率)
    VARP det1 = mDetectHead1->forward(neck_feat1);
    det1 = mDetectHead1Bn->forward(det1);
    det1 = _Relu6(det1);

    // 中等目标检测
    VARP det2 = mDetectHead2->forward(neck_feat2);
    det2 = mDetectHead2Bn->forward(det2);
    det2 = _Relu6(det2);

    // 大目标检测 (低分辨率)
    VARP det3 = mDetectHead3->forward(neck_feat3);
    det3 = mDetectHead3Bn->forward(det3);
    det3 = _Relu6(det3);

    // 简化版：只使用最大特征图进行检测
    // 在实际应用中，应该对所有三个尺度进行检测并合并结果
    VARP output = mOutputConv->forward(det3);

    // 转换回NCHW格式用于损失计算
    output = _Convert(output, NCHW);

    // 检查输出是否有效
    if (output.get() == nullptr) {
        cout << "Error: Null output from YOLOv10 model" << endl;
        return {};
    }

    // 对于训练，我们需要将输出reshape为适合损失计算的格式
    auto shape = output->getInfo();
    if (!shape || shape->dim.size() != 4) {
        cout << "Error: Invalid output shape from YOLOv10 model" << endl;
        return {};
    }

    int batch = shape->dim[0];
    int channels = shape->dim[1];
    int height = shape->dim[2];
    int width = shape->dim[3];

    cout << "YOLOv10 output shape: [" << batch << ", " << channels << ", " << height << ", " << width << "]" << endl;

    // 简化输出处理 - 直接返回适合分类的格式
    // 使用全局平均池化将空间维度压缩
    output = _ReduceMean(output, {2, 3}); // [B, C]
    output = _Unsqueeze(output, {2}); // [B, C, 1] 为了兼容后续处理

    return {output};
}

inline void YOLOv10Model::printModelInfo() const {
    cout << "=== YOLOv10 Model Information ===" << endl;
    cout << "Architecture: Simplified YOLOv10 for Mobile Training" << endl;
    cout << "Input Size: " << mInputSize << "x" << mInputSize << "x3" << endl;
    cout << "Number of Classes: " << mNumClasses << endl;
    cout << "Backbone: Simplified CSPNet (4 stages)" << endl;
    cout << "Neck: Simplified PAN (Path Aggregation Network)" << endl;
    cout << "Head: Single detection head" << endl;
    cout << "Output Channels: " << (5 + mNumClasses) << " (4 bbox + 1 objectness + " << mNumClasses << " classes)" << endl;
    cout << "Optimized for: Mobile GPU training (FP32)" << endl;
    cout << "Training Mode: End-to-end object detection" << endl;
    cout << "=================================" << endl;
}

#endif // YOLOV10_MODEL_HPP
