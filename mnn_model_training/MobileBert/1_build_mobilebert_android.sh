#!/bin/bash

# MobileBert Android Training Build Script
# 专门用于编译MobileBert手机端训练程序

set -e

echo "=== MobileBert Android Training Build Script ==="

# 检查Android NDK环境
if [ -z "$ANDROID_NDK" ]; then
    export ANDROID_NDK=/home/<USER>/software/softwareDev/AndroidSDK/ndk/26.3.11579264
    echo "Setting ANDROID_NDK to: $ANDROID_NDK"
fi

if [ ! -d "$ANDROID_NDK" ]; then
    echo "Error: Android NDK not found at $ANDROID_NDK"
    echo "Please set ANDROID_NDK environment variable or install Android NDK"
    exit 1
fi

# 设置构建目录
BUILD_DIR="../../project/android/build_64"
MNN_ROOT="../../"
MOBILEBERT_DIR="$(pwd)"

echo "Android NDK: $ANDROID_NDK"
echo "Build Directory: $BUILD_DIR"
echo "MNN Root: $MNN_ROOT"
echo "MobileBert Directory: $MOBILEBERT_DIR"

echo ""
echo "=== Synchronizing MobileBert Training Code ==="

# 确保训练代码在正确的编译位置（在改变目录之前）
DEMO_DIR="../../tools/train/source/demo"
MOBILEBERT_SOURCE="MobileBertTrainingDemo.cpp"

if [ -f "$MOBILEBERT_SOURCE" ]; then
    echo "Found MobileBert training code: $MOBILEBERT_SOURCE"
    echo "Copying to demo directory: $DEMO_DIR/"

    # 创建备份（如果目标文件已存在）
    if [ -f "$DEMO_DIR/MobileBertTrainingDemo.cpp" ]; then
        echo "Creating backup of existing file..."
        cp "$DEMO_DIR/MobileBertTrainingDemo.cpp" "$DEMO_DIR/MobileBertTrainingDemo.cpp.backup"
    fi

    # 复制新的训练代码和依赖文件
    cp "$MOBILEBERT_SOURCE" "$DEMO_DIR/"

    # 复制MobileBert模型头文件
    if [ -f "MobileBertModel.hpp" ]; then
        cp "MobileBertModel.hpp" "$DEMO_DIR/"
        echo "Copied MobileBertModel.hpp"
    fi

    # 验证复制是否成功
    if [ -f "$DEMO_DIR/MobileBertTrainingDemo.cpp" ]; then
        echo "✅ Training code synchronized successfully"
        echo "   Source: $(pwd)/$MOBILEBERT_SOURCE"
        echo "   Target: $DEMO_DIR/MobileBertTrainingDemo.cpp"
    else
        echo "❌ Error: Failed to copy training code"
        exit 1
    fi
else
    echo "❌ Error: MobileBertTrainingDemo.cpp not found in current directory"
    echo "Please ensure the training code exists: $(pwd)/$MOBILEBERT_SOURCE"
    exit 1
fi

# 创建构建目录并切换到构建目录
echo ""
echo "=== Preparing Build Directory ==="
mkdir -p $BUILD_DIR
echo "Changing to build directory: $BUILD_DIR"
cd $BUILD_DIR

echo ""
echo "=== Configuring CMake for MobileBert Android Training ==="

# 配置CMake - 启用训练和GPU支持
cmake ../../../ \
    -DCMAKE_TOOLCHAIN_FILE=$ANDROID_NDK/build/cmake/android.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Release \
    -DANDROID_ABI="arm64-v8a" \
    -DANDROID_STL=c++_static \
    -DMNN_USE_LOGCAT=false \
    -DMNN_BUILD_BENCHMARK=ON \
    -DMNN_USE_SSE=OFF \
    -DMNN_BUILD_TEST=ON \
    -DMNN_BUILD_TRAIN=ON \
    -DMNN_BUILD_TRAIN_MINI=ON \
    -DMNN_OPENCL=ON \
    -DMNN_ARM82=ON \
    -DANDROID_NATIVE_API_LEVEL=android-21 \
    -DMNN_BUILD_FOR_ANDROID_COMMAND=true \
    -DNATIVE_LIBRARY_OUTPUT=. -DNATIVE_INCLUDE_OUTPUT=. $*

if [ $? -ne 0 ]; then
    echo "Error: CMake configuration failed"
    exit 1
fi

echo ""
echo "=== Building MobileBert Training Program ==="

# 编译训练程序
make runTrainDemo.out -j4

if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

echo ""
echo "=== Build Summary ==="
echo "✅ MobileBert training program built successfully!"
echo ""
echo "Source code used:"
echo "  - MobileBert training code: $MOBILEBERT_DIR/MobileBertTrainingDemo.cpp"
echo "  - Compiled from: $DEMO_DIR/MobileBertTrainingDemo.cpp"
echo ""
echo "Generated files in $BUILD_DIR:"
echo "  - runTrainDemo.out (Training executable)"
echo "  - libMNN.so (MNN core library)"
echo "  - libMNN_Express.so (MNN expression library)"
echo "  - libMNN_CL.so (OpenCL GPU library)"
echo "  - libMNNTrain.so (Training library)"
echo "  - libMNNTrainUtils.so (Training utilities)"
echo ""
echo "Features enabled:"
echo "  ✅ GPU Training (OpenCL)"
echo "  ✅ FP32 Precision"
echo "  ✅ ARM64 Optimization"
echo "  ✅ MobileBert Text Classification Training"
echo ""
echo "Next steps:"
echo "  1. Run: ./2_deploy_mobilebert_android.sh to deploy to device"
echo "  2. Or manually push files to Android device"

cd - > /dev/null
echo ""
echo "Build completed successfully! 🎉"
