#!/usr/bin/env python3
"""
生成MobileBert文本分类训练数据
为MobileBert模型在手机端GPU训练创建模拟的文本分类数据集
"""

import os
import sys
import random
import json
from pathlib import Path

def create_text_samples(base_dir, num_classes=2, samples_per_class=256):
    """
    创建文本分类样本
    
    Args:
        base_dir: 基础目录
        num_classes: 类别数量
        samples_per_class: 每个类别的样本数量
    
    Returns:
        bool: 是否成功创建
    """
    
    # 创建目录结构
    train_dir = os.path.join(base_dir, "train")
    os.makedirs(train_dir, exist_ok=True)
    
    # 模拟文本数据 - 不同类别的文本模板
    text_templates = {
        0: [  # 正面情感
            "This is a great product and I love it very much",
            "Excellent quality and fast delivery service",
            "Amazing experience and highly recommended",
            "Perfect item exactly as described",
            "Outstanding customer service and support",
            "Wonderful product with great value for money",
            "Fantastic quality and quick shipping",
            "Impressive features and easy to use",
            "Superb design and excellent functionality",
            "Brilliant product that exceeded expectations"
        ],
        1: [  # 负面情感
            "This product is terrible and waste of money",
            "Poor quality and very disappointing",
            "Awful experience and would not recommend",
            "Defective item and bad customer service",
            "Horrible product with many problems",
            "Terrible quality and slow delivery",
            "Disappointing features and hard to use",
            "Poor design and limited functionality",
            "Bad product that failed expectations",
            "Worst purchase ever made online"
        ]
    }
    
    # 生成训练数据文件列表
    train_txt_path = os.path.join(base_dir, "train.txt")
    
    total_samples = 0
    
    with open(train_txt_path, 'w') as txt_file:
        for class_id in range(num_classes):
            print(f"Generating class {class_id} samples...")
            
            for sample_idx in range(samples_per_class):
                # 生成文件名
                filename = f"class{class_id:02d}_sample{sample_idx:03d}.txt"
                filepath = os.path.join(train_dir, filename)
                
                # 选择文本模板并添加变化
                base_text = random.choice(text_templates[class_id])
                
                # 添加一些随机变化
                variations = [
                    f"{base_text}.",
                    f"{base_text}!",
                    f"I think {base_text.lower()}.",
                    f"In my opinion, {base_text.lower()}.",
                    f"Honestly, {base_text.lower()}.",
                    f"Actually, {base_text.lower()}.",
                ]
                
                final_text = random.choice(variations)
                
                # 写入文本文件
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(final_text)
                
                # 写入标签文件
                txt_file.write(f"{filename} {class_id}\n")
                
                total_samples += 1
    
    return total_samples

def calculate_dataset_size(base_dir):
    """计算数据集大小"""
    total_size = 0
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            filepath = os.path.join(root, file)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size

def create_test_dataset(target_iterations=20, max_batch_size=16, epochs=2, num_classes=2):
    """
    创建MobileBert测试数据集
    
    Args:
        target_iterations: 目标迭代次数
        max_batch_size: 最大batch size
        epochs: 训练轮数
        num_classes: 分类类别数
    
    Returns:
        bool: 是否成功创建
    """
    
    print("🚀 MobileBert Text Classification Dataset Generator")
    print("=" * 60)
    
    # 计算所需样本数
    min_samples_needed = target_iterations * max_batch_size
    samples_per_class = min_samples_needed // num_classes
    
    # 确保每个类别至少有足够的样本
    if samples_per_class < 128:
        samples_per_class = 128
    
    total_samples = samples_per_class * num_classes
    
    print(f"📊 Dataset Configuration:")
    print(f"  Target iterations per epoch: {target_iterations}")
    print(f"  Max batch size: {max_batch_size}")
    print(f"  Number of epochs: {epochs}")
    print(f"  Number of classes: {num_classes}")
    print(f"  Samples per class: {samples_per_class}")
    print(f"  Total samples: {total_samples}")
    print()
    
    # 创建数据集目录
    base_dir = "mobilebert_test_data"
    if os.path.exists(base_dir):
        print(f"⚠️  Directory {base_dir} already exists. Removing...")
        import shutil
        shutil.rmtree(base_dir)
    
    os.makedirs(base_dir, exist_ok=True)
    print(f"📁 Created directory: {base_dir}")
    
    # 生成文本样本
    print(f"\n📝 Generating text classification samples...")
    created_samples = create_text_samples(base_dir, num_classes, samples_per_class)
    
    if created_samples != total_samples:
        print(f"❌ Error: Expected {total_samples} samples, but created {created_samples}")
        return False
    
    print(f"✅ Successfully created {created_samples} text samples")
    
    # 验证数据集
    train_txt_path = os.path.join(base_dir, "train.txt")
    if not os.path.exists(train_txt_path):
        print(f"❌ Error: train.txt not found")
        return False
    
    # 统计文件
    with open(train_txt_path, 'r') as f:
        lines = f.readlines()
    
    print(f"\n📈 Dataset Statistics:")
    print(f"  Total text files: {len(lines)}")
    
    # 按类别统计
    class_counts = {}
    for line in lines:
        parts = line.strip().split()
        if len(parts) >= 2:
            class_id = int(parts[1])
            class_counts[class_id] = class_counts.get(class_id, 0) + 1
    
    for class_id, count in sorted(class_counts.items()):
        print(f"  Class {class_id}: {count} samples")
    
    # 计算数据集大小
    total_size = calculate_dataset_size(base_dir)
    print(f"  Total dataset size: {total_size / (1024*1024):.2f} MB")
    
    # 验证batch size支持
    print(f"\n🔍 Batch Size Validation:")
    for batch_size in [1, 2, 4, 8, 16]:
        if batch_size <= max_batch_size:
            iterations = min(target_iterations, total_samples // batch_size)
            print(f"  Batch size {batch_size:2d}: {iterations:2d} iterations per epoch")
    
    # 显示目录结构
    print(f"\n📂 Generated directory structure:")
    for root, dirs, files in os.walk(base_dir):
        level = root.replace(base_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files[:5]:  # 只显示前5个文件
            print(f"{subindent}{file}")
        if len(files) > 5:
            print(f"{subindent}... and {len(files)-5} more files")
    
    print(f"\n🎉 MobileBert text classification data generation completed successfully!")
    print(f"\nNext steps:")
    print(f"  1. Run: ./1_build_mobilebert_android.sh")
    print(f"  2. Run: ./2_deploy_mobilebert_android.sh")
    print(f"  3. Run: ./3_mobilebert_training.sh")
    
    return True

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Generate text classification data for MobileBert training')
    parser.add_argument('--iterations', type=int, default=20,
                       help='Target iterations per epoch (default: 20)')
    parser.add_argument('--max-batch-size', type=int, default=16,
                       help='Maximum batch size to support (default: 16)')
    parser.add_argument('--epochs', type=int, default=2,
                       help='Number of epochs (default: 2)')
    parser.add_argument('--num-classes', type=int, default=2,
                       help='Number of classification classes (default: 2)')

    args = parser.parse_args()

    try:
        success = create_test_dataset(
            target_iterations=args.iterations,
            max_batch_size=args.max_batch_size,
            epochs=args.epochs,
            num_classes=args.num_classes
        )
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
