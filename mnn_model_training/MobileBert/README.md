# MobileBert Mobile Training

MobileBert模型在Android设备上的GPU训练实现，用于文本分类任务。

## 📁 目录结构

```
MobileBert/
├── README.md                           # 本文档
├── MobileBertModel.hpp                 # MobileBert模型结构定义
├── MobileBertTrainingDemo.cpp          # MobileBert训练代码
├── create_mobilebert_data.py           # 文本分类数据生成脚本
├── 1_build_mobilebert_android.sh       # Android编译脚本
├── 2_deploy_mobilebert_android.sh      # Android部署脚本
├── 3_mobilebert_training.sh            # 训练执行脚本
└── mobilebert_test_data/               # 生成的训练数据目录
    ├── train/                          # 文本文件目录
    │   ├── class00_sample000.txt       # 类别0的文本样本
    │   ├── class01_sample000.txt       # 类别1的文本样本
    │   └── ...
    └── train.txt                       # 标签文件
```

## 🚀 快速开始

### 1. 生成训练数据

```bash
cd /path/to/mnn/mnn_model_training/MobileBert
python3 create_mobilebert_data.py
```

### 2. 编译MobileBert训练程序

```bash
chmod +x 1_build_mobilebert_android.sh
./1_build_mobilebert_android.sh
```

### 3. 部署到Android设备

```bash
chmod +x 2_deploy_mobilebert_android.sh
./2_deploy_mobilebert_android.sh
```

### 4. 在Android设备上运行训练

```bash
chmod +x 3_mobilebert_training.sh
./3_mobilebert_training.sh
```

或者手动执行：

```bash
# 连接到设备
adb shell

# 进入训练目录
cd /data/local/tmp/mobilebert_training

# 运行MobileBert GPU训练
LD_LIBRARY_PATH=. ./runTrainDemo.out MobileBertTrainingDemo ./mobilebert_test_data/train/ ./mobilebert_test_data/train.txt 3 1
```

## 🔧 技术特性

### 模型架构
- **基础模型**: google/mobilebert-uncased
- **隐藏层大小**: 512
- **层数**: 24层
- **注意力头数**: 4
- **词汇表大小**: 30522
- **最大序列长度**: 128 (优化为手机端训练)
- **分类类别**: 2 (二分类任务)

### 训练配置
- **后端**: GPU (OpenCL)
- **精度**: FP32
- **优化器**: SGD
- **学习率**: 0.001
- **损失函数**: CrossEntropy
- **支持的Batch Size**: 1, 2, 4, 8, 16

### 数据格式
- **输入**: 文本文件 (.txt)
- **标签**: filename label 格式
- **预处理**: 简化的tokenization (模拟真实tokenizer)

## 📊 性能特点

### 内存优化
- 减少序列长度至128 (原始512)
- 使用较小的embedding size (128)
- 支持小batch size训练

### GPU加速
- OpenCL后端支持
- FP32精度训练
- 高性能模式配置

## 🎯 使用示例

### 基本训练
```bash
# GPU训练，batch size 1
./3_mobilebert_training.sh 1

# CPU训练，batch size 1
adb shell 'cd /data/local/tmp/mobilebert_training && LD_LIBRARY_PATH=. ./runTrainDemo.out MobileBertTrainingDemo ./mobilebert_test_data/train/ ./mobilebert_test_data/train.txt 0 1'
```

### 批量测试
```bash
# 测试所有batch size
./3_mobilebert_training.sh

# 测试特定batch size
./3_mobilebert_training.sh 1 4 8
```

### 自定义数据
```bash
# 生成更多数据
python3 create_mobilebert_data.py --iterations 50 --max-batch-size 16 --num-classes 3

# 使用自定义数据训练
adb shell 'cd /data/local/tmp/mobilebert_training && LD_LIBRARY_PATH=. ./runTrainDemo.out MobileBertTrainingDemo /path/to/custom/data/ /path/to/custom/labels.txt 3 4'
```

## 🔍 故障排除

### 常见问题

1. **编译错误**
   - 检查Android NDK路径设置
   - 确认CMake版本兼容性
   - 验证依赖库是否完整

2. **GPU初始化失败**
   - 检查设备是否支持OpenCL
   - 确认libMNN_CL.so已正确部署
   - 尝试使用CPU后端 (backend_type=0)

3. **内存不足**
   - 减少batch size
   - 减少序列长度
   - 检查设备可用内存

4. **训练数据问题**
   - 确保数据文件存在且格式正确
   - 检查标签文件格式 (filename label)
   - 验证文本编码为UTF-8

### 调试命令

```bash
# 检查设备信息
adb shell 'cat /proc/cpuinfo | grep -E "processor|model name" | head -8'

# 检查内存使用
adb shell 'cat /proc/meminfo | grep -E "MemTotal|MemFree|MemAvailable"'

# 检查OpenCL支持
adb shell 'ls /system/lib64/*opencl* /vendor/lib64/*opencl* 2>/dev/null || echo "OpenCL libraries not found"'

# 查看训练日志
tail -f mobilebert_batch_results_*.log
```

## 📈 扩展功能

### 支持更多任务
- 修改NUM_CLASSES常量支持多分类
- 调整模型输出层维度
- 更新损失函数和评估指标

### 优化性能
- 调整序列长度 (MAX_SEQ_LENGTH)
- 使用混合精度训练
- 实现动态batch size

### 真实数据集
- 集成真实的tokenizer
- 支持更复杂的文本预处理
- 添加数据增强功能

## 📝 开发说明

### 代码结构
- `MobileBertModel.hpp`: 完整的MobileBert架构实现
- `MobileBertTrainingDemo.cpp`: 训练流程和数据处理
- `create_mobilebert_data.py`: 模拟数据生成工具

### 编译配置
```cmake
-DMNN_BUILD_TRAIN=ON          # 启用训练功能
-DMNN_BUILD_TRAIN_MINI=ON     # 启用轻量训练
-DMNN_OPENCL=ON               # 启用OpenCL GPU支持
-DMNN_ARM82=ON                # 启用ARM高级指令集
```

### 关键配置
```cpp
// GPU配置
MNNForwardType backend = MNN_FORWARD_OPENCL;
BackendConfig config;
config.precision = BackendConfig::Precision_High; // FP32
config.power = BackendConfig::Power_High;         // 高性能模式
```

## 🎉 总结

这个MobileBert训练实现提供了：
- ✅ 完整的MobileBert模型架构
- ✅ 手机端GPU训练支持
- ✅ 文本分类任务实现
- ✅ 自动化编译和部署流程
- ✅ 多batch size性能测试
- ✅ 详细的日志和错误处理

适用于在Android设备上进行轻量级的文本分类模型训练和研究。
