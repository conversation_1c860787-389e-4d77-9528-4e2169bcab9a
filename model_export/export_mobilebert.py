import torch
from transformers import MobileBertForSequenceClassification, MobileBertTokenizer

# Load model and tokenizer
model_name = "google/mobilebert-uncased"
tokenizer = MobileBertTokenizer.from_pretrained(model_name)
model = MobileBertForSequenceClassification.from_pretrained(model_name)
model.eval() # Set to evaluation mode

# Define batch sizes and sequence length
batch_sizes = [1, 4, 8, 16, 32, 64]
sequence_length = 512

# Export ONNX models for different batch sizes
for batch_size in batch_sizes:
    print(f"Exporting model with batch_size={batch_size}, sequence_length={sequence_length}")
    
    # Create dummy inputs with fixed batch size and sequence length
    input_ids = torch.ones(batch_size, sequence_length, dtype=torch.long)
    attention_mask = torch.ones(batch_size, sequence_length, dtype=torch.long)
    token_type_ids = torch.zeros(batch_size, sequence_length, dtype=torch.long)
    
    # Export to ONNX with fixed dimensions
    output_onnx_path = f"mobilebert_model_batch{batch_size}_seq{sequence_length}.onnx"
    torch.onnx.export(
        model,
        (input_ids, attention_mask, token_type_ids),
        output_onnx_path,
        input_names=["input_ids", "attention_mask", "token_type_ids"],
        output_names=["logits"],
        # No dynamic_axes - using fixed dimensions
        opset_version=14 # Choose a compatible ONNX opset version
    )
    print(f"Model exported to {output_onnx_path}")

print("All models exported successfully!")