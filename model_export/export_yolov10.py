from ultralytics import YOLO
import shutil
from pathlib import Path

# Load your YOLOv10 model
model = YOLO("yolov10m.pt")  

# Define batch sizes for export
batch_sizes = [1, 4, 8, 16, 32, 64]
image_size = 320

# Export ONNX models for different batch sizes
for batch_size in batch_sizes:
    print(f"Exporting YOLOv10 model with batch_size={batch_size}")
    
    # Export to ONNX with fixed batch size (uses default filename)
    model.export(
        format="onnx", 
        dynamic=False,  # Use fixed dimensions instead of dynamic
        batch=batch_size,  # Set specific batch size for this export
        imgsz=image_size,  # Standard YOLOv10 input size
        # Each model will be optimized for its specific batch size
    )
    
    # Get the default exported filename and create custom filename
    default_onnx_file = Path("yolov10m.onnx")  # Default export filename
    custom_onnx_file = Path(f"yolov10m_batch{batch_size}_imgsz{image_size}.onnx")
    
    # Rename the exported file to custom filename
    if default_onnx_file.exists():
        shutil.move(str(default_onnx_file), str(custom_onnx_file))
        print(f"Model exported to {custom_onnx_file}")
    else:
        print(f"Warning: Expected file {default_onnx_file} not found")

print("All YOLOv10 models exported successfully!")
print("Exported models:")
for batch_size in batch_sizes:
    print(f"  - yolov10m_batch{batch_size}_imgsz{image_size}.onnx (batch_size={batch_size})") 
