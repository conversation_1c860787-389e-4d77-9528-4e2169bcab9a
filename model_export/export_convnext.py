from transformers import ConvNextImageProcessor, ConvNextForImageClassification
import torch

# Load model and processor
model_name = "facebook/convnext-base-384"
processor = ConvNextImageProcessor.from_pretrained(model_name)
model = ConvNextForImageClassification.from_pretrained(model_name)
model.eval()  # Set to evaluation mode

# Define batch sizes and image dimensions
batch_sizes = [1, 4, 8, 16, 32, 64]
image_size = 384  # ConvNext-base-384 uses 384x384 input
channels = 3  # RGB channels

# Export ONNX models for different batch sizes
for batch_size in batch_sizes:
    print(f"Exporting ConvNext model with batch_size={batch_size}, image_size={image_size}")
    
    # Create dummy inputs with fixed batch size and image dimensions
    # ConvNext expects pixel_values input
    pixel_values = torch.randn(batch_size, channels, image_size, image_size)
    
    # Export to ONNX with fixed dimensions
    output_onnx_path = f"convnext_base_384_batch{batch_size}_imgsz{image_size}.onnx"
    torch.onnx.export(
        model,
        pixel_values,  # ConvNext model expects pixel_values directly
        output_onnx_path,
        input_names=["pixel_values"],
        output_names=["logits"],
        # No dynamic_axes - using fixed dimensions
        opset_version=14  # Choose a compatible ONNX opset version
    )
    print(f"Model exported to {output_onnx_path}")

print("All ConvNext models exported successfully!")
print(f"\nModel details:")
print(f"  - Model: {model_name}")
print(f"  - Input size: {channels}x{image_size}x{image_size}")
print(f"  - Number of classes: {model.config.num_labels}")
print(f"  - Supported batch sizes: {batch_sizes}")
