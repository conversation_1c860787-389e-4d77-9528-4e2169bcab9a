import torch
import timm
import argparse
from pathlib import Path

# EfficientNet model configurations
EFFICIENTNET_CONFIGS = {
    "b0": {"model": "efficientnet_b0.ra_in1k", "size": 224},
    "b1": {"model": "efficientnet_b1.ft_in1k", "size": 240},
    "b2": {"model": "efficientnet_b2.ra_in1k", "size": 260},
    "b3": {"model": "efficientnet_b3.ra2_in1k", "size": 300},
    "b4": {"model": "efficientnet_b4.ra2_in1k", "size": 380},
    "b5": {"model": "efficientnet_b5.sw_in12k_ft_in1k", "size": 456},
    "b6": {"model": "efficientnet_b6.untrained", "size": 528},
    "b7": {"model": "efficientnet_b7.ra_in1k", "size": 600},
}

def export_efficientnet_models(
    variant="b3",
    batch_sizes=[1, 4, 8, 16, 32, 64],
    output_dir="./",
    opset_version=14
):
    """
    Export EfficientNet models to ONNX format for different batch sizes.
    
    Args:
        variant (str): EfficientNet variant (b0-b7)
        batch_sizes (list): List of batch sizes to export
        output_dir (str): Output directory for ONNX files
        opset_version (int): ONNX opset version
    """
    
    if variant not in EFFICIENTNET_CONFIGS:
        print(f"Error: Unsupported variant '{variant}'. Available: {list(EFFICIENTNET_CONFIGS.keys())}")
        return
    
    config = EFFICIENTNET_CONFIGS[variant]
    model_name = config["model"]
    image_size = config["size"]
    
    print(f"Loading EfficientNet model: {model_name}")
    print(f"Input resolution: {image_size}x{image_size}")
    
    # Load the model
    try:
        model = timm.create_model(model_name, pretrained=True)
        model.eval()
    except Exception as e:
        print(f"Error loading model {model_name}: {e}")
        print("Available EfficientNet models:")
        available_models = timm.list_models("efficientnet*")
        for i, model in enumerate(available_models[:15]):  # Show first 15
            print(f"  - {model}")
        if len(available_models) > 15:
            print(f"  ... and {len(available_models) - 15} more")
        return
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    channels = 3  # RGB channels
    print(f"Model input shape will be: (batch_size, {channels}, {image_size}, {image_size})")
    print(f"Number of classes: {model.num_classes}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()) / 1e6:.1f}M")
    
    exported_files = []
    
    # Export ONNX models for different batch sizes
    for batch_size in batch_sizes:
        print(f"Exporting EfficientNet-{variant.upper()} with batch_size={batch_size}")
        
        # Create dummy input with specified batch size and image dimensions
        dummy_input = torch.randn(batch_size, channels, image_size, image_size)
        
        # Create output filename
        output_filename = f"efficientnet_{variant}_batch{batch_size}_imgsz{image_size}.onnx"
        output_file_path = output_path / output_filename
        
        try:
            # Export to ONNX with fixed dimensions
            torch.onnx.export(
                model,
                dummy_input,
                str(output_file_path),
                input_names=["input"],
                output_names=["logits"],
                opset_version=opset_version,
                do_constant_folding=True,
                export_params=True,
                verbose=False
            )
            
            exported_files.append(output_filename)
            print(f"✓ Model exported to {output_file_path}")
            
        except Exception as e:
            print(f"✗ Error exporting batch_size={batch_size}: {e}")
    
    # Summary
    print(f"\nExport completed!")
    print(f"Successfully exported {len(exported_files)} models:")
    for filename in exported_files:
        print(f"  - {filename}")
    
    print(f"\nModel details:")
    print(f"  - Architecture: EfficientNet-{variant.upper()}")
    print(f"  - Full model name: {model_name}")
    print(f"  - Input size: {channels}x{image_size}x{image_size}")
    print(f"  - Number of classes: {model.num_classes}")
    print(f"  - Model parameters: {sum(p.numel() for p in model.parameters()) / 1e6:.1f}M")
    print(f"  - ONNX opset version: {opset_version}")
    print(f"  - Output directory: {output_path.absolute()}")

def main():
    parser = argparse.ArgumentParser(description="Export EfficientNet models to ONNX format")
    
    parser.add_argument(
        "--variant", 
        type=str, 
        default="b3",
        choices=list(EFFICIENTNET_CONFIGS.keys()),
        help="EfficientNet variant (default: b3)"
    )
    
    parser.add_argument(
        "--batch_sizes", 
        nargs="+", 
        type=int, 
        default=[1, 4, 8, 16, 32, 64],
        help="Batch sizes to export (default: 1 4 8 16 32 64)"
    )
    
    parser.add_argument(
        "--output_dir", 
        type=str, 
        default="./",
        help="Output directory (default: current directory)"
    )
    
    parser.add_argument(
        "--opset_version", 
        type=int, 
        default=14,
        help="ONNX opset version (default: 14)"
    )
    
    parser.add_argument(
        "--list_variants", 
        action="store_true",
        help="List available EfficientNet variants"
    )
    
    parser.add_argument(
        "--list_models", 
        action="store_true",
        help="List all available EfficientNet models in timm"
    )
    
    args = parser.parse_args()
    
    if args.list_variants:
        print("Available EfficientNet variants:")
        for variant, config in EFFICIENTNET_CONFIGS.items():
            model_name = config["model"]
            size = config["size"]
            print(f"  {variant}: {model_name} (input: {size}x{size})")
        return
    
    if args.list_models:
        print("All available EfficientNet models in timm:")
        models = timm.list_models("efficientnet*")
        for model in models:
            print(f"  - {model}")
        return
    
    export_efficientnet_models(
        variant=args.variant,
        batch_sizes=args.batch_sizes,
        output_dir=args.output_dir,
        opset_version=args.opset_version
    )

if __name__ == "__main__":
    main() 